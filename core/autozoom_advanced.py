"""
🔍 Advanced AutoZoom Controller
==============================

Real AutoZoom functionality with PTZ-aware features and advanced mathematical framework.
Implements actual zoom operations with priority scoring and confidence monitoring.

Built with love for worker protection and safety! 💙
"""

import cv2
import numpy as np
import time
from typing import List, Dict, Tuple, Optional, Any
from dataclasses import dataclass, field
from collections import deque
import logging

logger = logging.getLogger(__name__)

@dataclass
class ZoomOperation:
    """
    🔍 Zoom Operation Record
    
    Tracks individual zoom operations for performance analysis
    """
    track_id: int
    start_time: float
    end_time: Optional[float] = None
    priority_score: float = 0.0
    initial_confidence: float = 0.0
    final_confidence: float = 0.0
    zoom_scale: float = 2.0
    success: bool = False
    reason: str = ""

class AdvancedAutoZoomController:
    """
    🔍 Advanced AutoZoom Controller
    
    Real AutoZoom functionality with:
    - PTZ-aware priority scoring
    - Energy-based confidence monitoring
    - Multi-person zoom management
    - Performance tracking and analytics
    """
    
    def __init__(self):
        """Initialize advanced AutoZoom controller"""
        
        logger.info("🔍 Initializing Advanced AutoZoom Controller...")
        
        # Zoom state management
        self.current_zoom_target = None
        self.zoom_start_time = None
        self.zoom_scale = 2.0
        self.is_zooming = False
        
        # Priority and confidence thresholds
        self.confidence_threshold = 0.5
        self.priority_threshold = 0.3  # Lower threshold for more zoom operations
        self.max_zoom_duration = 3.0
        self.zoom_cooldown = 0.5  # Shorter cooldown
        
        # Performance tracking
        self.zoom_operations = []
        self.total_zoom_ops = 0
        self.successful_zooms = 0
        self.last_zoom_end_time = 0.0
        
        # Track confidence history
        self.track_confidence_history = {}
        self.confidence_window_size = 10
        
        logger.info("✅ Advanced AutoZoom Controller initialized")
    
    def update(self, tracks: List, frame_time: float) -> Tuple[Optional, bool, Dict[str, Any]]:
        """
        🎯 Update AutoZoom controller
        
        Args:
            tracks: List of PTZ-aware tracks
            frame_time: Current frame timestamp
        
        Returns:
            Tuple of (zoom_target, zoom_active, zoom_info)
        """
        
        # Update track confidence histories
        self._update_confidence_histories(tracks)
        
        # Check if current zoom should end
        if self.is_zooming:
            should_end_zoom = self._should_end_zoom(tracks, frame_time)
            if should_end_zoom:
                self._end_zoom(frame_time)
        
        # Check if new zoom should start
        if not self.is_zooming and self._can_start_zoom(frame_time):
            zoom_target = self._select_zoom_target(tracks)
            if zoom_target:
                self._start_zoom(zoom_target, frame_time)
        
        # Prepare zoom info
        zoom_info = self._get_zoom_info(tracks, frame_time)
        
        return self.current_zoom_target, self.is_zooming, zoom_info
    
    def _update_confidence_histories(self, tracks: List):
        """Update confidence history for all tracks"""
        
        for track in tracks:
            if track.track_id not in self.track_confidence_history:
                self.track_confidence_history[track.track_id] = deque(maxlen=self.confidence_window_size)
            
            # Calculate confidence based on tracking quality and energy
            confidence = self._calculate_track_confidence(track)
            self.track_confidence_history[track.track_id].append(confidence)
    
    def _calculate_track_confidence(self, track) -> float:
        """Calculate confidence score for a track"""
        
        # Base confidence from tracking quality
        base_confidence = getattr(track, 'tracking_quality', 0.8)
        
        # Energy-based confidence (higher energy = better visibility)
        energy = getattr(track, 'ptz_compensated_energy', 0)
        if energy > 0:
            energy_factor = min(1.0, energy / 1000.0)
        else:
            energy_factor = 0.5
        
        # Safety equipment visibility factor
        equipment_factor = 1.0
        helmet_features = getattr(track, 'helmet_features', None)
        if helmet_features:
            equipment_factor = helmet_features.get('confidence', 0.8)
        
        # Combined confidence
        confidence = (base_confidence * 0.5 + 
                     energy_factor * 0.3 + 
                     equipment_factor * 0.2)
        
        return max(0.0, min(1.0, confidence))
    
    def _should_end_zoom(self, tracks: List, frame_time: float) -> bool:
        """Check if current zoom should end"""
        
        if not self.current_zoom_target:
            return True
        
        # Find current target in tracks
        target_track = None
        for track in tracks:
            if track.track_id == self.current_zoom_target.track_id:
                target_track = track
                break
        
        # End zoom if target lost
        if not target_track:
            return True
        
        # End zoom if confidence recovered
        if self.current_zoom_target.track_id in self.track_confidence_history:
            recent_confidences = list(self.track_confidence_history[self.current_zoom_target.track_id])
            if len(recent_confidences) >= 3:
                avg_confidence = np.mean(recent_confidences[-3:])
                if avg_confidence > 0.7:  # Recovery threshold
                    return True
        
        # End zoom if maximum duration exceeded
        if frame_time - self.zoom_start_time > self.max_zoom_duration:
            return True
        
        return False
    
    def _can_start_zoom(self, frame_time: float) -> bool:
        """Check if new zoom can start"""
        
        # Check cooldown period
        if frame_time - self.last_zoom_end_time < self.zoom_cooldown:
            return False
        
        return True
    
    def _select_zoom_target(self, tracks: List):
        """Select best zoom target based on priority scoring"""
        
        candidates = []
        
        for track in tracks:
            if track.track_id not in self.track_confidence_history:
                continue
            
            # Calculate priority score
            priority_score = self._calculate_priority_score(track)
            
            if priority_score > self.priority_threshold:
                candidates.append((track, priority_score))
        
        if not candidates:
            return None
        
        # Select highest priority candidate
        candidates.sort(key=lambda x: x[1], reverse=True)
        return candidates[0][0]
    
    def _calculate_priority_score(self, track) -> float:
        """Calculate priority score for zoom target selection"""
        
        if track.track_id not in self.track_confidence_history:
            return 0.0
        
        confidence_history = list(self.track_confidence_history[track.track_id])
        if len(confidence_history) < 3:
            return 0.0
        
        # Current confidence level
        current_confidence = confidence_history[-1]
        avg_confidence = np.mean(confidence_history[-5:]) if len(confidence_history) >= 5 else current_confidence
        
        # Confidence deficit (higher deficit = higher priority)
        confidence_deficit = max(0.0, self.confidence_threshold - avg_confidence)
        
        # Energy potential (normalized energy for recovery potential)
        energy_potential = 0.5
        energy_history = getattr(track, 'energy_history', None)
        if energy_history and len(energy_history) > 0:
            avg_energy = np.mean(list(energy_history))
            energy_potential = min(1.0, avg_energy / 1000.0)
        
        # Safety criticality (helmet detection confidence)
        safety_criticality = 0.5
        helmet_features = getattr(track, 'helmet_features', None)
        if helmet_features:
            helmet_confidence = helmet_features.get('confidence', 0.8)
            safety_criticality = 1.0 - helmet_confidence  # Lower helmet confidence = higher criticality
        
        # Temporal urgency (time since last zoom)
        temporal_urgency = min(1.0, (time.time() - self.last_zoom_end_time) / 10.0)
        
        # Combined priority score
        priority_score = (confidence_deficit * 0.4 + 
                         energy_potential * 0.3 + 
                         safety_criticality * 0.2 + 
                         temporal_urgency * 0.1)
        
        return priority_score
    
    def _start_zoom(self, target_track, frame_time: float):
        """Start zoom operation on target track"""
        
        self.current_zoom_target = target_track
        self.zoom_start_time = frame_time
        self.is_zooming = True
        self.total_zoom_ops += 1
        
        # Record zoom operation
        initial_confidence = 0.0
        if target_track.track_id in self.track_confidence_history:
            recent_confidences = list(self.track_confidence_history[target_track.track_id])
            initial_confidence = recent_confidences[-1] if recent_confidences else 0.0
        
        priority_score = self._calculate_priority_score(target_track)
        
        zoom_op = ZoomOperation(
            track_id=target_track.track_id,
            start_time=frame_time,
            priority_score=priority_score,
            initial_confidence=initial_confidence,
            zoom_scale=self.zoom_scale
        )
        
        self.zoom_operations.append(zoom_op)
        
        logger.info(f"🔍 Starting zoom on track {target_track.track_id} (priority: {priority_score:.2f})")
    
    def _end_zoom(self, frame_time: float):
        """End current zoom operation"""
        
        if not self.current_zoom_target:
            return
        
        # Update zoom operation record
        if self.zoom_operations:
            current_op = self.zoom_operations[-1]
            current_op.end_time = frame_time
            
            # Calculate final confidence
            if self.current_zoom_target.track_id in self.track_confidence_history:
                recent_confidences = list(self.track_confidence_history[self.current_zoom_target.track_id])
                current_op.final_confidence = recent_confidences[-1] if recent_confidences else 0.0
                
                # Determine success
                confidence_improvement = current_op.final_confidence - current_op.initial_confidence
                if confidence_improvement > 0.1 or current_op.final_confidence > 0.7:
                    current_op.success = True
                    current_op.reason = "Confidence improved"
                    self.successful_zooms += 1
                else:
                    current_op.reason = "No improvement"
            
        logger.info(f"🔍 Ending zoom on track {self.current_zoom_target.track_id}")
        
        # Reset zoom state
        self.current_zoom_target = None
        self.zoom_start_time = None
        self.is_zooming = False
        self.last_zoom_end_time = frame_time
    
    def _get_zoom_info(self, tracks: List, frame_time: float) -> Dict[str, Any]:
        """Get current zoom information for visualization"""
        
        zoom_info = {
            'is_zooming': self.is_zooming,
            'zoom_target': self.current_zoom_target,
            'zoom_scale': self.zoom_scale,
            'zoom_progress': 0.0,
            'total_zoom_ops': self.total_zoom_ops,
            'successful_zooms': self.successful_zooms,
            'success_rate': self.successful_zooms / max(1, self.total_zoom_ops),
            'track_confidences': {},
            'priority_scores': {}
        }
        
        # Calculate zoom progress
        if self.is_zooming and self.zoom_start_time:
            elapsed = frame_time - self.zoom_start_time
            zoom_info['zoom_progress'] = min(1.0, elapsed / self.max_zoom_duration)
        
        # Add track confidences and priority scores
        for track in tracks:
            if track.track_id in self.track_confidence_history:
                recent_confidences = list(self.track_confidence_history[track.track_id])
                zoom_info['track_confidences'][track.track_id] = recent_confidences[-1] if recent_confidences else 0.0
                zoom_info['priority_scores'][track.track_id] = self._calculate_priority_score(track)
        
        return zoom_info
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get AutoZoom performance statistics"""
        
        stats = {
            'total_zoom_operations': self.total_zoom_ops,
            'successful_zooms': self.successful_zooms,
            'success_rate': self.successful_zooms / max(1, self.total_zoom_ops),
            'average_zoom_duration': 0.0,
            'confidence_improvements': 0,
            'active_tracks': len(self.track_confidence_history)
        }
        
        # Calculate average zoom duration
        completed_ops = [op for op in self.zoom_operations if op.end_time is not None]
        if completed_ops:
            durations = [op.end_time - op.start_time for op in completed_ops]
            stats['average_zoom_duration'] = np.mean(durations)
            
            # Count confidence improvements
            improvements = [op for op in completed_ops if op.final_confidence > op.initial_confidence]
            stats['confidence_improvements'] = len(improvements)
        
        return stats
