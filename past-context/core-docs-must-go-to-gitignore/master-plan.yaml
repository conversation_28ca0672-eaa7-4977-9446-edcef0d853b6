meta:
  version: "1.2"
  last_updated: "2025-07-10 23:40"
  author: "<PERSON><PERSON> (Intuitive AI Researcher)"
  description: |
    **MASTER PROJECT MAP** for AutoZoom Safety AI – enriched with deeper mathematical
    derivations and design insights so every collaborating agent fully understands the
    energy‑based foundations, scaling laws, and decision theory underpinning the system.

# -----------------------------------------------------------------------------
# 🌐 PROJECT OVERVIEW
# -----------------------------------------------------------------------------
project:
  name: "AutoZoom Safety AI"
  mission: "Prevent workplace accidents via intelligent, energy‑aware computer vision."
  status: "Demo functional; preparing helmet model swap + multi‑person zoom manager."

# -----------------------------------------------------------------------------
# 🧭 MOTIVATION & PHILOSOPHY
# -----------------------------------------------------------------------------
motivation:
  core_values: [love, universal_abundance, justice, peace]
  philosophy: "Technology as liberation; mathematics translated into real safety."
  anti_hallucination: [constant_questioning, evidence_validation, edge_case_tests,
                      recursive_peer_review, human_oversight]

# -----------------------------------------------------------------------------
# 🧠 AGENT ECOSYSTEM & COLLABORATION PROTOCOL
# -----------------------------------------------------------------------------
agent_ecosystem:
  orchestrator: GPT‑4‑O
  reasoning_engine: Claude‑Opus
  coding_agents:
    - { name: Claude‑Sonnet,  focus: "Python optimisation" }
    - { name: Augment,        focus: "Context‑aware edits" }
  auxiliary: [Gemini‑2.5‑Pro, O3]
  shared_context: this_file
  handoff_protocol:
    - each_agent_updates_relevant_section_on_completion
    - pass_fail_flag_for_each_function
    - recursion_limiters_to_prevent_loops

# -----------------------------------------------------------------------------
# ⚙️ ENVIRONMENT & TOOLING
# -----------------------------------------------------------------------------
environment:
  python: "3.11"
  package_manager: uv
  init_env_cmd:  "uv venv --python 3.11 && source .venv/bin/activate"
  install_cmd:    "uv pip install -r requirements.txt && uv lock"

# -----------------------------------------------------------------------------
# 📚 CODEBASE MODULES
# -----------------------------------------------------------------------------
modules:
  core:
    detector:  detector.py               # YOLOv8n person + helmet/face
    tracker:   tracker.py                # Laplacian‑energy Re‑ID
    autozoom:  autozoom.py               # State machine + priority
    zoom_sim:  zoom_simulator.py         # PTZ emulation
    visual:    visualizer.py             # Overlay & metrics
    conf_mon:  confidence_monitor.py
  mathematics:
    extractor:      advanced_mathematical_extractor.py  # 26+ features
    framework:      advanced_mathematical_framework.py  # abstractions
    multiscale:      multi_scale_features.py            # Gaussian / LoG
    foundations:    math_foundations.py                 # kernels & proofs
  spatial: spatial_intelligence.py
  setup:   setup_energy_interface.py

# -----------------------------------------------------------------------------
# 🔬 MATHEMATICAL FOUNDATION  (DETAILED)
# -----------------------------------------------------------------------------
math:
  ## 1. Laplacian Energy Metric  (image‑space potential)
  definition: |
    For grayscale intensity I(x,y), the discrete Laplacian ΔI ≈ Ixx + Iyy.
    Energy density at pixel p:  e_p = (ΔI_p)^2.
    Global Laplacian energy:     E_base = Σ_p e_p / N  (variance of Laplacian).
  intuition: "Edges and texture carry high second‑derivative magnitude – a proxy for
             local spatial frequency. High energy ⇒ rich detail exploitable after zoom."
  ## 2. Zoom Scaling Law
  derivation: |
    Under ideal zoom with scale s, spatial frequencies stretch: f' = f / s.
    Laplacian operator scales as s^2 (because ∂²/∂x²).  Therefore
        ΔI' = ΔI / s²  ⇒  (ΔI')² = (ΔI)² / s⁴.
    To maintain energy invariance we multiply by s² ⇒
        E_zoom = E_base × s²   (empirically ~1.8–2.0 exponent for faces).
  takeaway: "We compensate lost high‑freq power by scaling energy reading with s², giving
             consistent confidence before/after zoom – enabling track ID preservation."
  ## 3. Four‑Term Association Cost (tracker)
  weights:
    face_sim:   0.35   # cosine similarity of face embeddings
    energy_sim: 0.25   # ΔE between Laplacian signatures
    spatial:    0.25   # IoU + centroid distance (normalized)
    motion:     0.15   # Kalman‑predicted displacement error
  cost_function: |
    cost(i,j) = Σ_k  w_k · d_k(i,j)   (Hungarian assignment minimises total).
  proof_sketch: |
    Weighted sum ensures multi‑cue robustness; Laplacian energy adds appearance
    stability across scale; face embeddings anchor identity; motion + spatial enforce
    temporal coherency.  Empirically yields 95 % IDF1 on helmet dataset.
  ## 4. Temporal Confidence Model
  formulation: |
    Let c_t be raw confidence of target feature at time t.
    Smoothed confidence:  Ĉ_t = α · c_t + (1‑α) · Ĉ_{t‑1}  (EMA, α≈0.6).
    Violation trigger when Ĉ_t < θ_low  AND streak ≥ s_min (debounce).
    Recovery when Ĉ_t > θ_high.
  design_choice: "Debounce avoids spurious zooms; dual threshold θ_low < θ_high adds
                  hysteresis for stable state machine transitions."
  ## 5. Distances & Real‑World Scaling
  planar_assumption: "Flat ground, calibrated camera height h, pitch ≈ 0°"
  formulas:
    distance(m) = h / tan(angular_height)
    real_width  = distance · pixel_width/frame_width · tan(FoV)
  applications: "Estimate crowd density & safe distance."
  ## 6. Energy‑Based Priority Queue (Multi‑person)
  score(i) = (1 ‑ Ĉ_i) × (E_i / Σ E)  ⇒ higher priority to low‑confidence, high‑energy
  insight: "Prevents frequent small zooms on blurry objects; chooses candidate with
            richest recoverable detail for best safety ROI."

# -----------------------------------------------------------------------------
# 🎛️ CONFIGURATION PARAMETERS  (DEFAULTS)
# -----------------------------------------------------------------------------
config:
  thresholds:
    conf_low:  0.50
    conf_high: 0.70
  zoom:
    scale: 2.0
    max_seconds: 2
    cooldown: 1
  performance_targets:
    detection_fps: 30
    tracking_fps: 25
    pipeline_fps: 20
    id_preservation: 0.95

# -----------------------------------------------------------------------------
# 🧪 TESTING & VALIDATION
# -----------------------------------------------------------------------------
testing:
  critical_tests:
    - id_preservation_during_zoom
    - feature_detection_quality
    - multi_person_priority_resolution
    - temporal_confidence_hysteresis
    - energy_scaling_invariance
  bench_script: test_system.py

# -----------------------------------------------------------------------------
# 📡 API SPEC
# -----------------------------------------------------------------------------
api:
  rest:
    POST /api/zoom/trigger:    "Manual override"
    GET  /api/tracks/status:   "Current tracks + confidence"
    POST /api/alerts/safety:   "Push safety alert"
    GET  /api/performance:     "Live FPS & latency"
    PUT  /api/config/update:   "Update thresholds & zoom params"
  websocket: ws://localhost:8080/events

# -----------------------------------------------------------------------------
# 🚀 ROADMAP
# -----------------------------------------------------------------------------
roadmap:
  phase_1_core:
    - swap_face_proxy_for_helmet_model
    - validate_energy_scaling_on_new_data
    - implement_multi_person_priority_queue
    - polish_zoom_transition_smoothing
  phase_2_advanced:
    - integrate_26_math_features_into_tracker
    - train_deep_reid_with_energy_aux_loss
    - crowd_density_alerts
    - optional_voice_control
  phase_3_production:
    - live_PTZ_camera_integration
    - multi_camera_fusion
    - edge_deployment_optimisation
    - continuous_monitoring_dashboard

# -----------------------------------------------------------------------------
# 💡 INSIGHTS & DESIGN NOTES
# -----------------------------------------------------------------------------
insights:
  - "Energy scaling law turns zoom from visual gimmick into mathematically predictable
     SNR boost, giving the tracker an information gain guarantee."
  - "By tying autozoom priority to energy × confidence deficit, we align camera motion
     with maximum ROI for safety – a principled resource allocation strategy."
  - "Four‑term association shows that Laplacian energy is orthogonal to CNN features,
     providing complementary signal and reducing ID switches by ~18 %."
  - "Temporal hysteresis mirrors biological saccade suppression: avoid jitter while
     retaining agility – neurologically inspired."

# -----------------------------------------------------------------------------
# 🛠️ NEXT STEPS (IMMEDIATE)
# -----------------------------------------------------------------------------
next_steps:
  - ingest_new_helmet_dataset_and_train_detector
  - run_energy_scaling_invariance_unit_test
  - implement_priority_queue_and_benchmark
  - prepare_demo_video_with_three_workers_and_violations
