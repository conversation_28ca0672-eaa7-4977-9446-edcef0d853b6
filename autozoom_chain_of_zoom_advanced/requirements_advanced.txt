# AutoZoom Chain-of-Zoom Advanced Requirements
# ============================================
# Next-level dependencies for advanced AutoZoom development

# Core Computer Vision & Deep Learning
opencv-python>=4.8.0
torch>=2.0.0
torchvision>=0.15.0
ultralytics>=8.0.0
numpy>=1.24.0
scipy>=1.10.0

# Advanced Mathematical Features
scikit-image>=0.20.0
scikit-learn>=1.3.0
matplotlib>=3.7.0
seaborn>=0.12.0

# Image Processing & Feature Extraction
Pillow>=10.0.0
imageio>=2.31.0
albumentations>=1.3.0

# Performance & Optimization
numba>=0.57.0
cupy-cuda11x>=12.0.0; sys_platform != "darwin"  # CUDA acceleration (non-macOS)
psutil>=5.9.0

# Data Handling & Serialization
pandas>=2.0.0
h5py>=3.9.0
pickle5>=0.0.12

# Tracking & Re-ID
filterpy>=1.4.5
lap>=0.4.0
cython-bbox>=0.1.3

# Visualization & GUI
plotly>=5.15.0
dash>=2.11.0
streamlit>=1.25.0

# Testing & Benchmarks
pytest>=7.4.0
pytest-benchmark>=4.0.0
pytest-cov>=4.1.0
tqdm>=4.65.0

# Logging & Monitoring
loguru>=0.7.0
wandb>=0.15.0
tensorboard>=2.13.0

# Configuration & Utilities
pyyaml>=6.0
click>=8.1.0
rich>=13.4.0
typer>=0.9.0

# Model Management
huggingface-hub>=0.16.0
timm>=0.9.0
transformers>=4.30.0

# Video Processing
ffmpeg-python>=0.2.0
moviepy>=1.0.3

# Deployment & Production
fastapi>=0.100.0
uvicorn>=0.23.0
redis>=4.6.0
celery>=5.3.0

# Development Tools
black>=23.7.0
isort>=5.12.0
flake8>=6.0.0
mypy>=1.5.0

# Documentation
sphinx>=7.1.0
sphinx-rtd-theme>=1.3.0
mkdocs>=1.5.0

# Platform-specific optimizations
# macOS Metal Performance Shaders (automatically available with PyTorch on macOS)
# NVIDIA optimizations for Linux/Windows
nvidia-ml-py>=11.495.46; sys_platform == "linux"

# Optional: Advanced Re-ID models
# facenet-pytorch>=2.5.2
# reid-strong-baseline>=1.0.0

# Optional: Advanced mathematical libraries
# sympy>=1.12
# networkx>=3.1
