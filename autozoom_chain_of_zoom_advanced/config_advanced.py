"""
⚙️ AutoZoom Chain-of-Zoom Advanced Configuration
===============================================

Advanced configuration for next-level AutoZoom development with:
- Helmet detection integration
- 26+ mathematical features
- Multi-person priority management
- Chain-of-Zoom principles

Built with love for worker protection and safety! 💙
"""

import os
import torch
from dataclasses import dataclass, field
from typing import Tuple, Dict, Any, List
from enum import Enum

class DetectionMode(Enum):
    """Detection mode options"""
    FACE_PROXY = "face_proxy"           # Original face-based detection
    HELMET_DETECTION = "helmet_detection"  # YOLOv8 helmet detection
    MULTI_CLASS_SAFETY = "multi_class"  # Helmet + vest + glasses + gloves

class TrackingMode(Enum):
    """Tracking complexity levels"""
    BASIC_ENERGY = "basic_energy"       # Original Laplacian energy
    ENHANCED_ENERGY = "enhanced_energy" # + 26 mathematical features
    DEEP_REID = "deep_reid"            # + Deep learning Re-ID features
    FULL_CHAIN_OF_ZOOM = "full_chain"  # Complete Chain-of-Zoom implementation

class ZoomMode(Enum):
    """Zoom management modes"""
    SINGLE_TARGET = "single_target"     # Original single-person zoom
    MULTI_PERSON = "multi_person"       # Energy-based priority queue
    EMERGENCY_RESPONSE = "emergency"    # Emergency protocol activation

@dataclass
class AdvancedAutoZoomConfig:
    """
    🎯 Advanced AutoZoom Configuration
    
    Next-level configuration supporting:
    - Helmet detection models
    - Advanced mathematical features
    - Multi-person priority management
    - Chain-of-Zoom principles
    """
    
    # ============================================================================
    # 🔍 ADVANCED DETECTION CONFIGURATION
    # ============================================================================
    
    # Detection Mode Selection
    detection_mode: DetectionMode = DetectionMode.HELMET_DETECTION
    
    # Helmet Detection (Primary)
    helmet_model_path: str = "models/helmet_detector/yolov8_helmet.pt"
    helmet_confidence: float = 0.6              # Helmet detection threshold
    helmet_iou: float = 0.45                    # Non-max suppression IoU
    
    # Multi-Class Safety Equipment
    safety_equipment_models: Dict[str, str] = field(default_factory=lambda: {
        "helmet": "models/helmet_detector/yolov8_helmet.pt",
        "vest": "models/safety_equipment/yolov8_vest.pt", 
        "glasses": "models/safety_equipment/yolov8_glasses.pt",
        "gloves": "models/safety_equipment/yolov8_gloves.pt"
    })
    
    # Person Detection (Base)
    person_model: str = "yolov8n.pt"
    person_confidence: float = 0.5
    person_iou: float = 0.45
    
    # ============================================================================
    # 🎯 ADVANCED TRACKING CONFIGURATION
    # ============================================================================
    
    # Tracking Mode Selection
    tracking_mode: TrackingMode = TrackingMode.ENHANCED_ENERGY
    
    # Enhanced Energy Features (26+ Mathematical Features)
    enable_advanced_math_features: bool = True
    math_features_config: Dict[str, Any] = field(default_factory=lambda: {
        "directional_energy": True,
        "spatial_intelligence": True,
        "gaussian_scale_space": True,
        "laplacian_of_gaussian": True,
        "gradient_magnitude": True,
        "texture_analysis": True,
        "edge_density": True,
        "corner_detection": True,
        "optical_flow": True,
        "temporal_consistency": True
    })
    
    # Chain-of-Zoom Parameters
    enable_chain_of_zoom: bool = True
    ar2_modeling: bool = True                   # AR-2 modeling for extreme zoom
    intermediate_scales: List[float] = field(default_factory=lambda: [1.2, 1.5, 2.0])
    scale_state_generation: bool = True
    
    # Deep Re-ID Features
    enable_deep_reid: bool = True
    reid_model_path: str = "models/reid_features/resnet50_reid.pt"
    reid_feature_dim: int = 2048
    
    # Association Cost Weights (Enhanced 6-factor)
    association_weights: Dict[str, float] = field(default_factory=lambda: {
        "helmet_similarity": 0.30,      # Helmet feature matching
        "energy_consistency": 0.25,     # Laplacian energy similarity
        "spatial_proximity": 0.20,      # IoU + centroid distance
        "motion_prediction": 0.15,      # Kalman prediction error
        "deep_reid_features": 0.10,     # Deep Re-ID similarity
    })
    
    # ============================================================================
    # 📊 ADVANCED CONFIDENCE MONITORING
    # ============================================================================
    
    # Multi-Modal Confidence
    confidence_modes: List[str] = field(default_factory=lambda: [
        "helmet_detection", "vest_detection", "posture_analysis"
    ])
    
    # Confidence Thresholds (Helmet-specific)
    helmet_confidence_threshold: float = 0.6    # Trigger zoom below this
    helmet_recovery_threshold: float = 0.8      # Consider recovered above this
    
    # Multi-Modal Weighting
    confidence_weights: Dict[str, float] = field(default_factory=lambda: {
        "helmet": 0.6,          # Primary safety indicator
        "vest": 0.3,            # Secondary safety indicator  
        "posture": 0.1          # Behavioral indicator
    })
    
    # Advanced Confidence Smoothing
    confidence_smoothing_method: str = "exponential_moving_average"
    confidence_ema_alpha: float = 0.6
    confidence_temporal_window: int = 10
    
    # ============================================================================
    # 🔍 ADVANCED AUTOZOOM LOGIC
    # ============================================================================
    
    # Zoom Mode Selection
    zoom_mode: ZoomMode = ZoomMode.MULTI_PERSON
    
    # Multi-Person Priority Management
    enable_priority_queue: bool = True
    max_concurrent_zooms: int = 1               # Start with single zoom
    priority_calculation_method: str = "energy_weighted_confidence_deficit"
    
    # Priority Scoring Parameters
    priority_weights: Dict[str, float] = field(default_factory=lambda: {
        "confidence_deficit": 0.4,      # (1 - confidence)
        "energy_potential": 0.3,        # Normalized energy
        "safety_criticality": 0.2,      # Emergency indicators
        "temporal_urgency": 0.1         # Time since last zoom
    })
    
    # Emergency Response
    enable_emergency_protocols: bool = True
    emergency_confidence_threshold: float = 0.3  # Critical safety threshold
    emergency_zoom_scale: float = 3.0           # Higher zoom for emergencies
    emergency_response_time: float = 0.5        # Faster response time
    
    # Zoom Parameters (Enhanced)
    zoom_scales: Dict[str, float] = field(default_factory=lambda: {
        "normal": 2.0,
        "priority": 2.5,
        "emergency": 3.0
    })
    
    max_zoom_duration: float = 3.0              # Increased for helmet analysis
    zoom_cooldown: float = 0.5                  # Reduced for faster response
    
    # ============================================================================
    # 📹 ADVANCED ZOOM SIMULATION
    # ============================================================================
    
    # Enhanced PTZ Simulation
    enable_realistic_ptz_artifacts: bool = True
    ptz_motion_blur: bool = True
    ptz_focus_adjustment: bool = True
    ptz_mechanical_delay: float = 0.1           # Simulate camera response time
    
    # Advanced Zoom Transitions
    zoom_transition_method: str = "smooth_bezier"
    zoom_transition_frames: int = 20            # Smoother transitions
    zoom_center_prediction: bool = True         # Predict target movement
    
    # Multi-Target Coordination
    enable_multi_target_transitions: bool = True
    target_switching_smoothing: float = 0.9
    
    # ============================================================================
    # 🚀 ADVANCED PERFORMANCE
    # ============================================================================
    
    # Performance Targets (Enhanced)
    target_detection_fps: int = 30
    target_tracking_fps: int = 25
    target_pipeline_fps: int = 20
    target_id_preservation: float = 0.95
    
    # GPU Acceleration
    enable_gpu_acceleration: bool = True
    batch_processing_size: int = 4
    model_quantization: bool = True
    
    # Multi-Threading
    enable_multithreading: bool = True
    detection_threads: int = 2
    tracking_threads: int = 2
    visualization_threads: int = 1
    
    # Memory Optimization
    enable_memory_optimization: bool = True
    max_track_history: int = 50
    feature_cache_size: int = 1000
    
    # ============================================================================
    # 🧪 TESTING & VALIDATION
    # ============================================================================
    
    # Test Configuration
    enable_comprehensive_testing: bool = True
    test_modes: List[str] = field(default_factory=lambda: [
        "helmet_detection_accuracy",
        "id_preservation_benchmark", 
        "multi_person_scenarios",
        "mathematical_features_validation",
        "performance_profiling"
    ])
    
    # Benchmark Targets
    benchmark_targets: Dict[str, float] = field(default_factory=lambda: {
        "helmet_detection_map": 0.85,   # mAP@0.5
        "id_preservation_idf1": 0.95,   # IDF1 score
        "multi_person_accuracy": 0.90,  # Multi-person handling
        "real_time_performance": 20.0   # FPS
    })
    
    # ============================================================================
    # 📁 ADVANCED FILE PATHS
    # ============================================================================
    
    # Model Directories
    models_base_dir: str = "models"
    helmet_models_dir: str = "models/helmet_detector"
    safety_equipment_dir: str = "models/safety_equipment"
    reid_models_dir: str = "models/reid_features"
    
    # Test Data
    test_videos_dir: str = "test_videos"
    benchmark_data_dir: str = "benchmarks/data"
    
    # Output Directories
    results_dir: str = "results_advanced"
    logs_dir: str = "logs"
    profiling_dir: str = "profiling"
    
    def __post_init__(self):
        """Initialize derived settings and validate advanced configuration"""
        
        # Create all necessary directories
        directories = [
            self.models_base_dir, self.helmet_models_dir, 
            self.safety_equipment_dir, self.reid_models_dir,
            self.test_videos_dir, self.benchmark_data_dir,
            self.results_dir, self.logs_dir, self.profiling_dir
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
        
        # Validate advanced parameters
        assert self.helmet_confidence > 0.0, "Helmet confidence must be positive"
        assert self.target_id_preservation <= 1.0, "ID preservation target must be <= 1.0"
        assert len(self.association_weights) >= 4, "Need at least 4 association factors"
        
        # Validate weights sum to 1.0
        assert abs(sum(self.association_weights.values()) - 1.0) < 0.01, "Association weights must sum to 1.0"
        assert abs(sum(self.confidence_weights.values()) - 1.0) < 0.01, "Confidence weights must sum to 1.0"
        
        # Calculate derived performance values
        self.max_zoom_frames = int(self.max_zoom_duration * self.target_pipeline_fps)
        self.zoom_cooldown_frames = int(self.zoom_cooldown * self.target_pipeline_fps)
        
    def get_device(self) -> str:
        """Get optimal device for advanced processing"""
        if self.enable_gpu_acceleration:
            if hasattr(torch, 'backends') and torch.backends.mps.is_available():
                return "mps"
            elif torch.cuda.is_available():
                return "cuda"
        return "cpu"
    
    def get_model_path(self, model_type: str) -> str:
        """Get path for specific model type"""
        model_paths = {
            "helmet": self.helmet_model_path,
            "person": self.person_model,
            "reid": self.reid_model_path
        }
        return model_paths.get(model_type, "")

# Global advanced configuration instance
config_advanced = AdvancedAutoZoomConfig()
