#!/usr/bin/env python3
"""
🚀 AutoZoom Chain-of-Zoom Advanced Demo
======================================

Next-level AutoZoom demonstration with:
- Helmet detection integration
- 26+ mathematical features
- Multi-person priority management
- Chain-of-Zoom principles
- Advanced performance optimization

Built with love for worker protection and safety! 💙
"""

import cv2
import numpy as np
import argparse
import time
import logging
from pathlib import Path
from typing import Dict, List, Optional
import sys

# Add current directory to path for imports
sys.path.append(str(Path(__file__).parent))

try:
    from config_advanced import config_advanced, DetectionMode, TrackingMode, ZoomMode
    from core.detector_advanced import AdvancedPersonSafetyDetector
    from mathematics.advanced_mathematical_extractor import AdvancedMathematicalExtractor
except ImportError as e:
    print(f"Import error: {e}")
    print("Please run: python setup_models.py --setup-all")
    sys.exit(1)

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AdvancedAutoZoomDemo:
    """
    🚀 Advanced AutoZoom Demo Application
    
    Demonstrates next-level AutoZoom capabilities:
    - Helmet detection instead of face proxy
    - 26+ mathematical features for tracking
    - Multi-person priority management
    - Chain-of-Zoom principles
    - Real-time performance optimization
    """
    
    def __init__(self, mode: str = "helmet_detection"):
        """Initialize advanced demo"""
        
        logger.info("🚀 Initializing AutoZoom Chain-of-Zoom Advanced Demo...")
        
        self.mode = mode
        self.frame_count = 0
        self.start_time = time.time()
        
        # Initialize components
        self.detector = AdvancedPersonSafetyDetector()
        self.math_extractor = AdvancedMathematicalExtractor()
        
        # Performance tracking
        self.processing_times = []
        self.fps_history = []
        
        logger.info(f"✅ Advanced demo initialized in {mode} mode")
    
    def run_video_demo(self, video_path: str, output_path: Optional[str] = None, 
                      display: bool = True) -> bool:
        """
        🎬 Run advanced video demonstration
        
        Args:
            video_path: Path to input video
            output_path: Optional output video path
            display: Whether to show real-time display
        
        Returns:
            Success status
        """
        
        logger.info(f"🎬 Starting advanced video demo: {video_path}")
        
        # Open video
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            logger.error(f"❌ Could not open video: {video_path}")
            return False
        
        # Get video properties
        fps = int(cap.get(cv2.CAP_PROP_FPS))
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        
        logger.info(f"📹 Video: {width}x{height} @ {fps}fps, {total_frames} frames")
        
        # Setup output video writer
        writer = None
        if output_path:
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            writer = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
            logger.info(f"📝 Output will be saved to: {output_path}")
        
        # Demo loop
        previous_frame = None
        
        try:
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                # Process frame
                processed_frame = self._process_frame(frame, previous_frame)
                
                # Display
                if display:
                    cv2.imshow('AutoZoom Advanced Demo', processed_frame)
                    
                    key = cv2.waitKey(1) & 0xFF
                    if key == ord('q'):
                        logger.info("Demo stopped by user")
                        break
                    elif key == ord('s'):
                        # Save current frame
                        save_path = f"results_advanced/frame_{self.frame_count:06d}.jpg"
                        cv2.imwrite(save_path, processed_frame)
                        logger.info(f"Frame saved: {save_path}")
                
                # Write to output
                if writer:
                    writer.write(processed_frame)
                
                # Update for next iteration
                previous_frame = frame.copy()
                self.frame_count += 1
                
                # Progress logging
                if self.frame_count % 100 == 0:
                    progress = (self.frame_count / total_frames) * 100
                    avg_fps = self._calculate_average_fps()
                    logger.info(f"Progress: {progress:.1f}% ({self.frame_count}/{total_frames}) @ {avg_fps:.1f} FPS")
        
        except KeyboardInterrupt:
            logger.info("Demo interrupted by user")
        
        finally:
            # Cleanup
            cap.release()
            if writer:
                writer.release()
            if display:
                cv2.destroyAllWindows()
        
        # Final statistics
        self._log_final_statistics()
        
        logger.info("🎉 Advanced demo completed successfully!")
        return True
    
    def _process_frame(self, frame: np.ndarray, previous_frame: Optional[np.ndarray]) -> np.ndarray:
        """Process single frame with advanced features"""
        
        start_time = time.time()
        
        # Step 1: Advanced Detection
        detections = self.detector.detect(frame)
        
        # Step 2: Mathematical Feature Extraction
        for detection in detections:
            if detection.bbox:
                features = self.math_extractor.extract_features(
                    frame, detection.bbox, zoom_level=1.0, previous_frame=previous_frame
                )
                # Store features in detection for tracking
                detection.mathematical_features = features
        
        # Step 3: Visualization
        processed_frame = self._visualize_advanced_features(frame, detections)
        
        # Performance tracking
        processing_time = time.time() - start_time
        self.processing_times.append(processing_time)
        
        return processed_frame
    
    def _visualize_advanced_features(self, frame: np.ndarray, detections: List) -> np.ndarray:
        """Visualize advanced features and detections"""
        
        vis_frame = frame.copy()
        
        # Draw detections
        for i, detection in enumerate(detections):
            x1, y1, x2, y2 = detection.bbox
            
            # Person bounding box
            cv2.rectangle(vis_frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
            
            # Safety equipment detection
            if detection.safety_equipment:
                for equipment_type, equipment_info in detection.safety_equipment.items():
                    if equipment_info and 'bbox' in equipment_info:
                        ex1, ey1, ex2, ey2 = equipment_info['bbox']
                        
                        # Color coding for different equipment
                        colors = {
                            'helmet': (0, 0, 255),    # Red
                            'vest': (255, 0, 0),      # Blue
                            'glasses': (0, 255, 255), # Yellow
                            'gloves': (255, 0, 255),  # Magenta
                            'face': (255, 255, 0)     # Cyan (fallback)
                        }
                        
                        color = colors.get(equipment_type, (128, 128, 128))
                        cv2.rectangle(vis_frame, (ex1, ey1), (ex2, ey2), color, 2)
                        
                        # Equipment label
                        label = f"{equipment_type}: {equipment_info['confidence']:.2f}"
                        cv2.putText(vis_frame, label, (ex1, ey1 - 10), 
                                  cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
            
            # Detection info
            info_text = f"ID:{i} Conf:{detection.confidence:.2f} Q:{detection.quality_score:.2f}"
            cv2.putText(vis_frame, info_text, (x1, y1 - 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
            
            # Mathematical features visualization (simplified)
            if hasattr(detection, 'mathematical_features'):
                features = detection.mathematical_features
                
                # Energy visualization
                energy_text = f"E:{features.laplacian_energy:.1f}"
                cv2.putText(vis_frame, energy_text, (x1, y2 + 20), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 255), 1)
                
                # Texture features
                if features.texture_energy > 0:
                    texture_text = f"T:{features.texture_energy:.2f}"
                    cv2.putText(vis_frame, texture_text, (x1, y2 + 40), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 128, 0), 1)
        
        # System information overlay
        self._draw_system_info(vis_frame)
        
        return vis_frame
    
    def _draw_system_info(self, frame: np.ndarray):
        """Draw system information overlay"""
        
        h, w = frame.shape[:2]
        
        # Performance info
        current_fps = self._calculate_current_fps()
        avg_fps = self._calculate_average_fps()
        
        info_lines = [
            f"AutoZoom Advanced Demo - Mode: {self.mode}",
            f"Frame: {self.frame_count}",
            f"FPS: {current_fps:.1f} (avg: {avg_fps:.1f})",
            f"Detection: {config_advanced.detection_mode.value}",
            f"Tracking: {config_advanced.tracking_mode.value}",
            f"Device: {config_advanced.get_device()}"
        ]
        
        # Draw background
        overlay_height = len(info_lines) * 25 + 20
        cv2.rectangle(frame, (10, 10), (400, overlay_height), (0, 0, 0), -1)
        cv2.rectangle(frame, (10, 10), (400, overlay_height), (255, 255, 255), 1)
        
        # Draw text
        for i, line in enumerate(info_lines):
            y_pos = 30 + i * 25
            cv2.putText(frame, line, (20, y_pos), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)
        
        # Performance indicators
        detector_stats = self.detector.get_performance_stats()
        if detector_stats:
            perf_text = f"Det: {detector_stats.get('detection_fps', 0):.1f} FPS"
            cv2.putText(frame, perf_text, (w - 200, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 1)
        
        math_stats = self.math_extractor.get_performance_stats()
        if math_stats:
            math_text = f"Math: {math_stats.get('extraction_fps', 0):.1f} FPS"
            cv2.putText(frame, math_text, (w - 200, 55), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 255), 1)
    
    def _calculate_current_fps(self) -> float:
        """Calculate current FPS"""
        if len(self.processing_times) < 10:
            return 0.0
        
        recent_times = self.processing_times[-10:]
        avg_time = np.mean(recent_times)
        return 1.0 / avg_time if avg_time > 0 else 0.0
    
    def _calculate_average_fps(self) -> float:
        """Calculate average FPS"""
        if self.frame_count == 0:
            return 0.0
        
        elapsed_time = time.time() - self.start_time
        return self.frame_count / elapsed_time if elapsed_time > 0 else 0.0
    
    def _log_final_statistics(self):
        """Log final performance statistics"""
        
        if not self.processing_times:
            return
        
        avg_processing_time = np.mean(self.processing_times)
        avg_fps = 1.0 / avg_processing_time if avg_processing_time > 0 else 0
        
        logger.info("📊 Final Performance Statistics:")
        logger.info(f"  Total frames processed: {self.frame_count}")
        logger.info(f"  Average processing time: {avg_processing_time*1000:.1f}ms")
        logger.info(f"  Average FPS: {avg_fps:.1f}")
        logger.info(f"  Total runtime: {time.time() - self.start_time:.1f}s")
        
        # Component statistics
        detector_stats = self.detector.get_performance_stats()
        if detector_stats:
            logger.info(f"  Detection FPS: {detector_stats.get('detection_fps', 0):.1f}")
        
        math_stats = self.math_extractor.get_performance_stats()
        if math_stats:
            logger.info(f"  Math extraction FPS: {math_stats.get('extraction_fps', 0):.1f}")

def main():
    """Main demo function"""
    
    parser = argparse.ArgumentParser(description="AutoZoom Chain-of-Zoom Advanced Demo")
    parser.add_argument("--video", type=str, default="test_videos/helmet_scenario.mp4",
                       help="Input video path")
    parser.add_argument("--output", type=str, default=None,
                       help="Output video path")
    parser.add_argument("--mode", type=str, default="helmet_detection",
                       choices=["helmet_detection", "multi_person", "mathematical_features"],
                       help="Demo mode")
    parser.add_argument("--no-display", action="store_true",
                       help="Run without display window")
    parser.add_argument("--setup-models", action="store_true",
                       help="Setup models before running demo")
    
    args = parser.parse_args()
    
    # Setup models if requested
    if args.setup_models:
        logger.info("Setting up models...")
        from setup_models import ModelDownloader
        downloader = ModelDownloader()
        if not downloader.setup_all():
            logger.error("Model setup failed")
            return 1
    
    # Check if video exists
    if not Path(args.video).exists():
        logger.error(f"Video file not found: {args.video}")
        logger.info("Available test videos should be in: test_videos/")
        return 1
    
    # Create output directory
    if args.output:
        output_dir = Path(args.output).parent
        output_dir.mkdir(parents=True, exist_ok=True)
    
    # Run demo
    try:
        demo = AdvancedAutoZoomDemo(mode=args.mode)
        success = demo.run_video_demo(
            video_path=args.video,
            output_path=args.output,
            display=not args.no_display
        )
        
        return 0 if success else 1
        
    except Exception as e:
        logger.error(f"Demo failed: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
