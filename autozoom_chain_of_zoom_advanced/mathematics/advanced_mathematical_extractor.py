"""
🧮 Advanced Mathematical Feature Extractor
==========================================

Implements 26+ mathematical features from the energy interface for enhanced
tracking and Chain-of-Zoom principles. Core mathematical foundation for
next-level AutoZoom performance.

Mathematical Features:
1. Laplacian Energy (E ∝ zoom²) - Original breakthrough
2. Directional Energy Decomposition
3. Gaussian Scale-Space Analysis
4. Spatial Intelligence Features
5. 3D Understanding from 2D Video
6. Advanced Safety Behavior Analysis

Built with love for worker protection and safety! 💙
"""

import cv2
import numpy as np
from scipy import ndimage, signal
from scipy.spatial.distance import cdist
from skimage import feature, measure, filters, segmentation
from skimage.feature import local_binary_pattern, graycomatrix, graycoprops
from typing import Dict, List, Tuple, Optional, Any
import logging
from dataclasses import dataclass
import time

logger = logging.getLogger(__name__)

@dataclass
class MathematicalFeatures:
    """
    🧮 Container for 26+ mathematical features
    
    Comprehensive feature set for advanced tracking and analysis
    """
    # Core Energy Features (1-5)
    laplacian_energy: float = 0.0
    gradient_magnitude_energy: float = 0.0
    directional_energy_x: float = 0.0
    directional_energy_y: float = 0.0
    energy_density_variance: float = 0.0
    
    # Scale-Space Features (6-10)
    gaussian_scale_space_energy: List[float] = None
    laplacian_of_gaussian_response: List[float] = None
    difference_of_gaussians: List[float] = None
    scale_invariant_features: List[float] = None
    multi_scale_edge_density: List[float] = None
    
    # Texture and Pattern Features (11-15)
    local_binary_pattern_histogram: np.ndarray = None
    gray_level_cooccurrence_features: Dict[str, float] = None
    texture_energy: float = 0.0
    texture_homogeneity: float = 0.0
    texture_contrast: float = 0.0
    
    # Spatial Intelligence Features (16-20)
    spatial_frequency_distribution: np.ndarray = None
    corner_response_strength: float = 0.0
    edge_orientation_histogram: np.ndarray = None
    spatial_coherence_measure: float = 0.0
    geometric_moment_invariants: np.ndarray = None
    
    # Temporal and Motion Features (21-26)
    optical_flow_magnitude: float = 0.0
    temporal_consistency_score: float = 0.0
    motion_boundary_strength: float = 0.0
    velocity_field_divergence: float = 0.0
    acceleration_field_magnitude: float = 0.0
    temporal_energy_conservation: float = 0.0
    
    # Additional Advanced Features (27+)
    safety_behavior_indicators: Dict[str, float] = None
    three_d_understanding_score: float = 0.0
    environmental_context_features: Dict[str, float] = None
    
    def __post_init__(self):
        if self.gaussian_scale_space_energy is None:
            self.gaussian_scale_space_energy = []
        if self.laplacian_of_gaussian_response is None:
            self.laplacian_of_gaussian_response = []
        if self.difference_of_gaussians is None:
            self.difference_of_gaussians = []
        if self.scale_invariant_features is None:
            self.scale_invariant_features = []
        if self.multi_scale_edge_density is None:
            self.multi_scale_edge_density = []
        if self.safety_behavior_indicators is None:
            self.safety_behavior_indicators = {}
        if self.environmental_context_features is None:
            self.environmental_context_features = {}

class AdvancedMathematicalExtractor:
    """
    🧮 Advanced Mathematical Feature Extractor
    
    Implements 26+ mathematical features for enhanced tracking performance:
    - Laplacian energy scaling (E ∝ zoom²)
    - Directional energy decomposition
    - Gaussian scale-space analysis
    - Spatial intelligence features
    - Temporal consistency validation
    - 3D understanding from 2D video
    """
    
    def __init__(self):
        """Initialize mathematical feature extractor"""
        
        logger.info("🧮 Initializing Advanced Mathematical Feature Extractor...")
        
        # Scale-space parameters
        self.scales = [1.0, 1.4, 2.0, 2.8, 4.0]  # Gaussian scales
        self.orientations = 8  # Number of orientation bins
        
        # Texture analysis parameters
        self.lbp_radius = 3
        self.lbp_n_points = 24
        
        # Performance tracking
        self.extraction_times = []
        
        logger.info("✅ Mathematical feature extractor initialized")
    
    def extract_features(self, frame: np.ndarray, bbox: Tuple[int, int, int, int],
                        zoom_level: float = 1.0, previous_frame: Optional[np.ndarray] = None) -> MathematicalFeatures:
        """
        🎯 Extract comprehensive mathematical features
        
        Args:
            frame: Current frame
            bbox: Bounding box (x1, y1, x2, y2)
            zoom_level: Current zoom level for energy scaling
            previous_frame: Previous frame for temporal features
        
        Returns:
            MathematicalFeatures object with all computed features
        """
        start_time = time.time()
        
        try:
            # Extract region of interest
            x1, y1, x2, y2 = bbox
            roi = frame[y1:y2, x1:x2]
            
            if roi.size == 0:
                return MathematicalFeatures()
            
            # Convert to grayscale for analysis
            gray_roi = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY) if len(roi.shape) == 3 else roi
            
            features = MathematicalFeatures()
            
            # Extract all feature categories
            self._extract_core_energy_features(gray_roi, features, zoom_level)
            self._extract_scale_space_features(gray_roi, features)
            self._extract_texture_features(gray_roi, features)
            self._extract_spatial_intelligence_features(gray_roi, features)
            
            if previous_frame is not None:
                self._extract_temporal_features(frame, previous_frame, bbox, features)
            
            self._extract_advanced_features(roi, gray_roi, features)
            
            # Performance tracking
            extraction_time = time.time() - start_time
            self.extraction_times.append(extraction_time)
            
            return features
            
        except Exception as e:
            logger.error(f"Feature extraction error: {e}")
            return MathematicalFeatures()
    
    def _extract_core_energy_features(self, gray_roi: np.ndarray, features: MathematicalFeatures, zoom_level: float):
        """Extract core energy features (1-5)"""
        
        # 1. Laplacian Energy (Original breakthrough: E ∝ zoom²)
        laplacian = cv2.Laplacian(gray_roi, cv2.CV_64F)
        base_energy = np.var(laplacian)
        features.laplacian_energy = base_energy * (zoom_level ** 2)
        
        # 2. Gradient Magnitude Energy
        grad_x = cv2.Sobel(gray_roi, cv2.CV_64F, 1, 0, ksize=3)
        grad_y = cv2.Sobel(gray_roi, cv2.CV_64F, 0, 1, ksize=3)
        gradient_magnitude = np.sqrt(grad_x**2 + grad_y**2)
        features.gradient_magnitude_energy = np.var(gradient_magnitude) * (zoom_level ** 1.8)
        
        # 3-4. Directional Energy Decomposition
        features.directional_energy_x = np.var(grad_x) * (zoom_level ** 2)
        features.directional_energy_y = np.var(grad_y) * (zoom_level ** 2)
        
        # 5. Energy Density Variance
        energy_density = laplacian ** 2
        features.energy_density_variance = np.var(energy_density)
    
    def _extract_scale_space_features(self, gray_roi: np.ndarray, features: MathematicalFeatures):
        """Extract scale-space features (6-10)"""
        
        # 6. Gaussian Scale-Space Energy
        for scale in self.scales:
            sigma = scale
            gaussian_filtered = filters.gaussian(gray_roi, sigma=sigma)
            energy = np.var(cv2.Laplacian(gaussian_filtered.astype(np.uint8), cv2.CV_64F))
            features.gaussian_scale_space_energy.append(energy)
        
        # 7. Laplacian of Gaussian Response
        for scale in self.scales:
            sigma = scale
            gaussian_filtered = filters.gaussian(gray_roi, sigma=sigma)
            # Use OpenCV Laplacian instead of skimage
            log_response = -sigma**2 * cv2.Laplacian(gaussian_filtered.astype(np.uint8), cv2.CV_64F)
            features.laplacian_of_gaussian_response.append(np.var(log_response))
        
        # 8. Difference of Gaussians
        for i in range(len(self.scales) - 1):
            sigma1, sigma2 = self.scales[i], self.scales[i + 1]
            gauss1 = filters.gaussian(gray_roi, sigma=sigma1)
            gauss2 = filters.gaussian(gray_roi, sigma=sigma2)
            dog = gauss1 - gauss2
            features.difference_of_gaussians.append(np.var(dog))
        
        # 9. Scale-Invariant Features (SIFT-like)
        try:
            sift = cv2.SIFT_create()
            keypoints = sift.detect(gray_roi, None)
            scale_responses = [kp.response for kp in keypoints]
            features.scale_invariant_features = scale_responses[:10]  # Top 10 responses
        except:
            features.scale_invariant_features = [0.0] * 10
        
        # 10. Multi-Scale Edge Density
        for scale in self.scales:
            sigma = scale
            edges = feature.canny(gray_roi, sigma=sigma)
            edge_density = np.sum(edges) / edges.size
            features.multi_scale_edge_density.append(edge_density)
    
    def _extract_texture_features(self, gray_roi: np.ndarray, features: MathematicalFeatures):
        """Extract texture and pattern features (11-15)"""
        
        # 11. Local Binary Pattern Histogram
        lbp = local_binary_pattern(gray_roi, self.lbp_n_points, self.lbp_radius, method='uniform')
        features.local_binary_pattern_histogram, _ = np.histogram(lbp.ravel(), bins=self.lbp_n_points + 2, 
                                                                 range=(0, self.lbp_n_points + 2))
        features.local_binary_pattern_histogram = features.local_binary_pattern_histogram.astype(float)
        features.local_binary_pattern_histogram /= np.sum(features.local_binary_pattern_histogram)
        
        # 12. Gray Level Co-occurrence Matrix Features
        try:
            # Reduce gray levels for GLCM computation
            gray_reduced = (gray_roi // 32).astype(np.uint8)  # 8 gray levels
            glcm = graycomatrix(gray_reduced, distances=[1], angles=[0, np.pi/4, np.pi/2, 3*np.pi/4], 
                              levels=8, symmetric=True, normed=True)
            
            features.gray_level_cooccurrence_features = {
                'contrast': np.mean(graycoprops(glcm, 'contrast')),
                'dissimilarity': np.mean(graycoprops(glcm, 'dissimilarity')),
                'homogeneity': np.mean(graycoprops(glcm, 'homogeneity')),
                'energy': np.mean(graycoprops(glcm, 'energy')),
                'correlation': np.mean(graycoprops(glcm, 'correlation'))
            }
        except:
            features.gray_level_cooccurrence_features = {
                'contrast': 0.0, 'dissimilarity': 0.0, 'homogeneity': 0.0, 
                'energy': 0.0, 'correlation': 0.0
            }
        
        # 13-15. Texture Energy, Homogeneity, Contrast
        features.texture_energy = features.gray_level_cooccurrence_features['energy']
        features.texture_homogeneity = features.gray_level_cooccurrence_features['homogeneity']
        features.texture_contrast = features.gray_level_cooccurrence_features['contrast']
    
    def _extract_spatial_intelligence_features(self, gray_roi: np.ndarray, features: MathematicalFeatures):
        """Extract spatial intelligence features (16-20)"""
        
        # 16. Spatial Frequency Distribution
        f_transform = np.fft.fft2(gray_roi)
        f_shift = np.fft.fftshift(f_transform)
        magnitude_spectrum = np.abs(f_shift)
        
        # Radial frequency distribution
        h, w = gray_roi.shape
        center_y, center_x = h // 2, w // 2
        y, x = np.ogrid[:h, :w]
        radius = np.sqrt((x - center_x)**2 + (y - center_y)**2)
        
        # Compute radial average
        radius_int = radius.astype(int)
        max_radius = min(center_x, center_y)
        radial_profile = np.zeros(max_radius)
        
        for r in range(max_radius):
            mask = (radius_int == r)
            if np.any(mask):
                radial_profile[r] = np.mean(magnitude_spectrum[mask])
        
        features.spatial_frequency_distribution = radial_profile
        
        # 17. Corner Response Strength
        corners = feature.corner_harris(gray_roi)
        features.corner_response_strength = np.sum(corners > 0.01 * corners.max())
        
        # 18. Edge Orientation Histogram
        grad_x = cv2.Sobel(gray_roi, cv2.CV_64F, 1, 0, ksize=3)
        grad_y = cv2.Sobel(gray_roi, cv2.CV_64F, 0, 1, ksize=3)
        orientation = np.arctan2(grad_y, grad_x)
        
        hist, _ = np.histogram(orientation.ravel(), bins=self.orientations, range=(-np.pi, np.pi))
        features.edge_orientation_histogram = hist.astype(float) / np.sum(hist)
        
        # 19. Spatial Coherence Measure
        # Measure of local spatial consistency
        coherence_kernel = np.array([[1, 1, 1], [1, -8, 1], [1, 1, 1]])
        coherence_response = cv2.filter2D(gray_roi.astype(np.float32), -1, coherence_kernel)
        features.spatial_coherence_measure = 1.0 / (1.0 + np.var(coherence_response))
        
        # 20. Geometric Moment Invariants (Hu moments)
        moments = cv2.moments(gray_roi)
        hu_moments = cv2.HuMoments(moments)
        features.geometric_moment_invariants = hu_moments.flatten()
    
    def _extract_temporal_features(self, frame: np.ndarray, previous_frame: np.ndarray, 
                                 bbox: Tuple[int, int, int, int], features: MathematicalFeatures):
        """Extract temporal and motion features (21-26)"""
        
        x1, y1, x2, y2 = bbox
        
        # Extract current and previous ROIs
        current_roi = cv2.cvtColor(frame[y1:y2, x1:x2], cv2.COLOR_BGR2GRAY)
        previous_roi = cv2.cvtColor(previous_frame[y1:y2, x1:x2], cv2.COLOR_BGR2GRAY)
        
        if current_roi.shape != previous_roi.shape or current_roi.size == 0:
            return
        
        # 21. Optical Flow Magnitude
        try:
            flow = cv2.calcOpticalFlowPyrLK(previous_roi, current_roi, None, None)
            if flow[0] is not None:
                flow_magnitude = np.sqrt(flow[0][:, :, 0]**2 + flow[0][:, :, 1]**2)
                features.optical_flow_magnitude = np.mean(flow_magnitude)
        except:
            features.optical_flow_magnitude = 0.0
        
        # 22. Temporal Consistency Score
        frame_diff = cv2.absdiff(current_roi, previous_roi)
        features.temporal_consistency_score = 1.0 / (1.0 + np.mean(frame_diff))
        
        # 23. Motion Boundary Strength
        motion_edges = cv2.Canny(frame_diff, 50, 150)
        features.motion_boundary_strength = np.sum(motion_edges) / motion_edges.size
        
        # 24-26. Advanced temporal features (simplified)
        features.velocity_field_divergence = np.var(frame_diff)
        features.acceleration_field_magnitude = np.mean(np.abs(frame_diff))
        
        # Energy conservation check
        current_energy = np.var(cv2.Laplacian(current_roi, cv2.CV_64F))
        previous_energy = np.var(cv2.Laplacian(previous_roi, cv2.CV_64F))
        features.temporal_energy_conservation = 1.0 / (1.0 + abs(current_energy - previous_energy))
    
    def _extract_advanced_features(self, roi: np.ndarray, gray_roi: np.ndarray, features: MathematicalFeatures):
        """Extract additional advanced features (27+)"""
        
        # Safety Behavior Indicators
        features.safety_behavior_indicators = {
            'posture_stability': self._calculate_posture_stability(gray_roi),
            'movement_pattern': self._analyze_movement_pattern(gray_roi),
            'equipment_visibility': self._assess_equipment_visibility(roi),
            'environmental_hazard': self._detect_environmental_hazards(roi)
        }
        
        # 3D Understanding Score
        features.three_d_understanding_score = self._estimate_3d_understanding(gray_roi)
        
        # Environmental Context Features
        features.environmental_context_features = {
            'lighting_quality': np.mean(roi) / 255.0,
            'contrast_level': np.std(gray_roi) / 255.0,
            'noise_level': self._estimate_noise_level(gray_roi),
            'occlusion_factor': self._estimate_occlusion(gray_roi)
        }
    
    def _calculate_posture_stability(self, gray_roi: np.ndarray) -> float:
        """Calculate posture stability indicator"""
        # Simplified posture analysis based on vertical symmetry
        h, w = gray_roi.shape
        left_half = gray_roi[:, :w//2]
        right_half = np.fliplr(gray_roi[:, w//2:])
        
        if left_half.shape != right_half.shape:
            return 0.5
        
        symmetry_score = 1.0 - np.mean(np.abs(left_half.astype(float) - right_half.astype(float))) / 255.0
        return max(0.0, min(1.0, symmetry_score))
    
    def _analyze_movement_pattern(self, gray_roi: np.ndarray) -> float:
        """Analyze movement pattern regularity"""
        # Simplified movement analysis using edge consistency
        edges = feature.canny(gray_roi)
        edge_density = np.sum(edges) / edges.size
        return min(1.0, edge_density * 10)  # Normalize to 0-1
    
    def _assess_equipment_visibility(self, roi: np.ndarray) -> float:
        """Assess safety equipment visibility"""
        # Simplified visibility assessment based on color distribution
        hsv = cv2.cvtColor(roi, cv2.COLOR_BGR2HSV)
        
        # Look for bright/reflective colors (safety equipment)
        bright_mask = hsv[:, :, 2] > 200  # High value (brightness)
        visibility_score = np.sum(bright_mask) / bright_mask.size
        
        return min(1.0, visibility_score * 5)  # Normalize
    
    def _detect_environmental_hazards(self, roi: np.ndarray) -> float:
        """Detect potential environmental hazards"""
        # Simplified hazard detection based on color and texture
        gray = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY)
        
        # Look for irregular textures (potential hazards)
        texture_variance = np.var(cv2.Laplacian(gray, cv2.CV_64F))
        hazard_score = min(1.0, texture_variance / 10000.0)
        
        return hazard_score
    
    def _estimate_3d_understanding(self, gray_roi: np.ndarray) -> float:
        """Estimate 3D understanding from 2D image"""
        # Simplified 3D estimation using depth cues
        
        # Gradient-based depth estimation
        grad_x = cv2.Sobel(gray_roi, cv2.CV_64F, 1, 0, ksize=3)
        grad_y = cv2.Sobel(gray_roi, cv2.CV_64F, 0, 1, ksize=3)
        
        gradient_magnitude = np.sqrt(grad_x**2 + grad_y**2)
        depth_score = np.var(gradient_magnitude) / 10000.0
        
        return min(1.0, depth_score)
    
    def _estimate_noise_level(self, gray_roi: np.ndarray) -> float:
        """Estimate noise level in image"""
        # High-frequency noise estimation
        kernel = np.array([[-1, -1, -1], [-1, 8, -1], [-1, -1, -1]])
        noise_response = cv2.filter2D(gray_roi.astype(np.float32), -1, kernel)
        noise_level = np.var(noise_response) / 10000.0
        
        return min(1.0, noise_level)
    
    def _estimate_occlusion(self, gray_roi: np.ndarray) -> float:
        """Estimate occlusion factor"""
        # Simplified occlusion estimation based on edge completeness
        edges = feature.canny(gray_roi)
        edge_completeness = np.sum(edges) / (gray_roi.shape[0] * 2 + gray_roi.shape[1] * 2)
        occlusion_factor = 1.0 - min(1.0, edge_completeness)
        
        return occlusion_factor
    
    def get_performance_stats(self) -> Dict[str, float]:
        """Get feature extraction performance statistics"""
        
        if not self.extraction_times:
            return {}
        
        recent_times = self.extraction_times[-100:]
        
        return {
            'avg_extraction_time': np.mean(recent_times),
            'extraction_fps': 1.0 / np.mean(recent_times) if recent_times else 0,
            'min_extraction_time': np.min(recent_times),
            'max_extraction_time': np.max(recent_times),
            'total_extractions': len(self.extraction_times)
        }
