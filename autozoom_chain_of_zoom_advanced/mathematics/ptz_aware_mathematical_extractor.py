"""
🎥 PTZ-Aware Mathematical Feature Extractor
==========================================

Enhanced mathematical framework that accounts for full PTZ (Pan, Tilt, Zoom)
camera movements. Bulletproof system for all camera movements while preserving
the core energy scaling innovations.

PTZ-Aware Features:
1. Camera Motion Compensation
2. Pan/Tilt Invariant Energy Scaling
3. Global Motion vs Object Motion Separation
4. PTZ-Aware Optical Flow Analysis
5. Camera Shake and Vibration Filtering
6. Perspective Distortion Correction

Mathematical Foundation:
- Original: E_zoom = E_base × (zoom_level)²
- Enhanced: E_ptz = E_base × (zoom_level)² × PTZ_compensation_factor

Built with love for worker protection and safety! 💙
"""

import cv2
import numpy as np
from scipy import ndimage, signal
from scipy.spatial.distance import cdist
from skimage import feature, measure, filters, segmentation
from typing import Dict, List, Tuple, Optional, Any, NamedTuple
import logging
from dataclasses import dataclass, field
import time

try:
    from .advanced_mathematical_extractor import AdvancedMathematicalExtractor, MathematicalFeatures
except ImportError:
    from advanced_mathematical_extractor import AdvancedMathematicalExtractor, MathematicalFeatures

logger = logging.getLogger(__name__)

@dataclass
class PTZState:
    """
    🎥 PTZ Camera State Information

    Tracks camera pan, tilt, zoom state for motion compensation
    """
    # Camera position
    pan_angle: float = 0.0          # Pan angle in degrees
    tilt_angle: float = 0.0         # Tilt angle in degrees
    zoom_level: float = 1.0         # Zoom magnification

    # Motion vectors
    pan_velocity: float = 0.0       # Pan velocity (deg/s)
    tilt_velocity: float = 0.0      # Tilt velocity (deg/s)
    zoom_velocity: float = 0.0      # Zoom velocity (x/s)

    # Camera characteristics
    field_of_view_h: float = 60.0   # Horizontal FOV in degrees
    field_of_view_v: float = 45.0   # Vertical FOV in degrees

    # Motion compensation
    global_motion_vector: Tuple[float, float] = (0.0, 0.0)
    camera_shake_magnitude: float = 0.0

    # Timestamp
    timestamp: float = 0.0

@dataclass
class PTZAwareMathematicalFeatures(MathematicalFeatures):
    """
    🎥 PTZ-Aware Mathematical Features

    Extends base mathematical features with PTZ-aware analysis
    """

    # PTZ-Specific Features (27-35)
    ptz_compensated_energy: float = 0.0
    global_motion_magnitude: float = 0.0
    local_motion_magnitude: float = 0.0
    camera_shake_filtered_energy: float = 0.0
    pan_invariant_features: np.ndarray = None
    tilt_invariant_features: np.ndarray = None
    perspective_corrected_energy: float = 0.0
    ptz_motion_consistency: float = 0.0
    stabilized_optical_flow: float = 0.0

    # Advanced PTZ Features (36-45)
    zoom_energy_conservation: float = 0.0
    pan_tilt_energy_conservation: float = 0.0
    multi_scale_ptz_features: List[float] = None
    ptz_aware_texture_stability: float = 0.0
    camera_motion_prediction_error: float = 0.0
    ptz_synchronized_tracking_score: float = 0.0

    def __post_init__(self):
        super().__post_init__()
        if self.pan_invariant_features is None:
            self.pan_invariant_features = np.zeros(8)
        if self.tilt_invariant_features is None:
            self.tilt_invariant_features = np.zeros(8)
        if self.multi_scale_ptz_features is None:
            self.multi_scale_ptz_features = []

class PTZAwareMathematicalExtractor(AdvancedMathematicalExtractor):
    """
    🎥 PTZ-Aware Mathematical Feature Extractor

    Enhanced extractor that accounts for full PTZ camera movements:

    Key Innovations:
    1. **Camera Motion Compensation**: Separates global camera motion from object motion
    2. **PTZ-Invariant Energy Scaling**: Maintains E ∝ zoom² regardless of pan/tilt
    3. **Perspective Correction**: Accounts for tilt-induced perspective distortion
    4. **Stabilized Optical Flow**: Filters out camera motion from object tracking
    5. **Multi-Scale PTZ Analysis**: Features stable across all PTZ operations
    """

    def __init__(self):
        """Initialize PTZ-aware mathematical feature extractor"""

        super().__init__()
        logger.info("🎥 Initializing PTZ-Aware Mathematical Feature Extractor...")

        # PTZ tracking
        self.ptz_history = []
        self.global_motion_estimator = None
        self.camera_calibration = None

        # Motion compensation parameters
        self.motion_compensation_enabled = True
        self.shake_filter_threshold = 2.0  # pixels
        self.global_motion_threshold = 5.0  # pixels

        # PTZ-aware scales (adjusted for camera movement)
        self.ptz_scales = [0.8, 1.0, 1.2, 1.6, 2.0, 2.8, 4.0]

        logger.info("✅ PTZ-aware mathematical feature extractor initialized")

    def extract_ptz_aware_features(self, frame: np.ndarray, bbox: Tuple[int, int, int, int],
                                  ptz_state: PTZState, previous_frame: Optional[np.ndarray] = None,
                                  previous_ptz_state: Optional[PTZState] = None) -> PTZAwareMathematicalFeatures:
        """
        🎯 Extract PTZ-aware mathematical features

        Args:
            frame: Current frame
            bbox: Bounding box (x1, y1, x2, y2)
            ptz_state: Current PTZ camera state
            previous_frame: Previous frame for temporal analysis
            previous_ptz_state: Previous PTZ state for motion analysis

        Returns:
            PTZAwareMathematicalFeatures with full PTZ compensation
        """
        start_time = time.time()

        try:
            # Extract base features first
            base_features = super().extract_features(frame, bbox, ptz_state.zoom_level, previous_frame)

            # Create PTZ-aware features
            ptz_features = PTZAwareMathematicalFeatures(**base_features.__dict__)

            # Extract region of interest
            x1, y1, x2, y2 = bbox
            roi = frame[y1:y2, x1:x2]

            if roi.size == 0:
                return ptz_features

            gray_roi = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY) if len(roi.shape) == 3 else roi

            # PTZ-specific feature extraction
            self._extract_ptz_compensated_energy(gray_roi, ptz_state, ptz_features)
            self._extract_motion_separation_features(frame, previous_frame, bbox, ptz_state,
                                                   previous_ptz_state, ptz_features)
            self._extract_ptz_invariant_features(gray_roi, ptz_state, ptz_features)
            self._extract_perspective_corrected_features(gray_roi, ptz_state, ptz_features)
            self._extract_advanced_ptz_features(roi, gray_roi, ptz_state, ptz_features)

            # Update PTZ history
            self.ptz_history.append(ptz_state)
            if len(self.ptz_history) > 50:  # Keep last 50 states
                self.ptz_history.pop(0)

            # Performance tracking
            extraction_time = time.time() - start_time
            self.extraction_times.append(extraction_time)

            return ptz_features

        except Exception as e:
            logger.error(f"PTZ-aware feature extraction error: {e}")
            # Return base features as fallback
            base_features = super().extract_features(frame, bbox, ptz_state.zoom_level, previous_frame)
            return PTZAwareMathematicalFeatures(**base_features.__dict__)

    def _extract_ptz_compensated_energy(self, gray_roi: np.ndarray, ptz_state: PTZState,
                                       features: PTZAwareMathematicalFeatures):
        """Extract PTZ-compensated energy features"""

        # Original Laplacian energy
        laplacian = cv2.Laplacian(gray_roi, cv2.CV_64F)
        base_energy = np.var(laplacian)

        # PTZ compensation factor
        ptz_compensation = self._calculate_ptz_compensation_factor(ptz_state)

        # Enhanced energy scaling: E_ptz = E_base × zoom² × PTZ_compensation
        features.ptz_compensated_energy = base_energy * (ptz_state.zoom_level ** 2) * ptz_compensation

        # Camera shake filtering
        if ptz_state.camera_shake_magnitude < self.shake_filter_threshold:
            features.camera_shake_filtered_energy = features.ptz_compensated_energy
        else:
            # Apply shake compensation
            shake_factor = 1.0 / (1.0 + ptz_state.camera_shake_magnitude / 10.0)
            features.camera_shake_filtered_energy = features.ptz_compensated_energy * shake_factor

    def _calculate_ptz_compensation_factor(self, ptz_state: PTZState) -> float:
        """Calculate PTZ compensation factor for energy scaling"""

        # Base compensation (no movement)
        compensation = 1.0

        # Pan compensation - account for horizontal motion blur
        pan_factor = 1.0 - min(0.3, abs(ptz_state.pan_velocity) / 100.0)

        # Tilt compensation - account for perspective changes
        tilt_factor = 1.0 - min(0.2, abs(ptz_state.tilt_velocity) / 100.0)

        # Zoom compensation - maintain energy consistency during zoom
        zoom_factor = 1.0 + (ptz_state.zoom_velocity * 0.1)  # Slight boost during zoom

        compensation = pan_factor * tilt_factor * zoom_factor

        return max(0.5, min(2.0, compensation))  # Clamp to reasonable range

    def _extract_motion_separation_features(self, frame: np.ndarray, previous_frame: Optional[np.ndarray],
                                          bbox: Tuple[int, int, int, int], ptz_state: PTZState,
                                          previous_ptz_state: Optional[PTZState],
                                          features: PTZAwareMathematicalFeatures):
        """Separate global camera motion from local object motion"""

        if previous_frame is None or previous_ptz_state is None:
            features.global_motion_magnitude = 0.0
            features.local_motion_magnitude = 0.0
            return

        # Estimate global motion from PTZ state changes
        pan_motion = (ptz_state.pan_angle - previous_ptz_state.pan_angle) * np.pi / 180.0
        tilt_motion = (ptz_state.tilt_angle - previous_ptz_state.tilt_angle) * np.pi / 180.0
        zoom_motion = ptz_state.zoom_level - previous_ptz_state.zoom_level

        # Convert to pixel motion (simplified)
        h, w = frame.shape[:2]
        pan_pixels = pan_motion * w / (ptz_state.field_of_view_h * np.pi / 180.0)
        tilt_pixels = tilt_motion * h / (ptz_state.field_of_view_v * np.pi / 180.0)

        global_motion_magnitude = np.sqrt(pan_pixels**2 + tilt_pixels**2)
        features.global_motion_magnitude = global_motion_magnitude

        # Calculate local motion (object motion relative to camera)
        x1, y1, x2, y2 = bbox
        current_roi = frame[y1:y2, x1:x2]
        previous_roi = previous_frame[y1:y2, x1:x2]

        if current_roi.shape == previous_roi.shape and current_roi.size > 0:
            # Optical flow for local motion
            current_gray = cv2.cvtColor(current_roi, cv2.COLOR_BGR2GRAY)
            previous_gray = cv2.cvtColor(previous_roi, cv2.COLOR_BGR2GRAY)

            try:
                # Dense optical flow
                flow = cv2.calcOpticalFlowPyrLK(previous_gray, current_gray, None, None)
                if flow[0] is not None:
                    flow_magnitude = np.sqrt(flow[0][:, :, 0]**2 + flow[0][:, :, 1]**2)
                    total_motion = np.mean(flow_magnitude)

                    # Subtract global motion to get local motion
                    features.local_motion_magnitude = max(0.0, total_motion - global_motion_magnitude)
                else:
                    features.local_motion_magnitude = 0.0
            except:
                features.local_motion_magnitude = 0.0
        else:
            features.local_motion_magnitude = 0.0

        # Stabilized optical flow (compensated for camera motion)
        if features.local_motion_magnitude > 0:
            features.stabilized_optical_flow = features.local_motion_magnitude
        else:
            features.stabilized_optical_flow = 0.0

    def _extract_ptz_invariant_features(self, gray_roi: np.ndarray, ptz_state: PTZState,
                                       features: PTZAwareMathematicalFeatures):
        """Extract features invariant to pan and tilt operations"""

        # Pan-invariant features (horizontal translation invariant)
        # Use vertical gradients and patterns
        grad_y = cv2.Sobel(gray_roi, cv2.CV_64F, 0, 1, ksize=3)

        # Vertical edge histogram (pan-invariant)
        vertical_edges = np.abs(grad_y)
        h, w = gray_roi.shape

        pan_invariant = []
        for i in range(8):  # 8 horizontal strips
            strip_start = i * h // 8
            strip_end = (i + 1) * h // 8
            strip_energy = np.mean(vertical_edges[strip_start:strip_end, :])
            pan_invariant.append(strip_energy)

        features.pan_invariant_features = np.array(pan_invariant)

        # Tilt-invariant features (vertical translation invariant)
        # Use horizontal gradients and patterns
        grad_x = cv2.Sobel(gray_roi, cv2.CV_64F, 1, 0, ksize=3)

        # Horizontal edge histogram (tilt-invariant)
        horizontal_edges = np.abs(grad_x)

        tilt_invariant = []
        for i in range(8):  # 8 vertical strips
            strip_start = i * w // 8
            strip_end = (i + 1) * w // 8
            strip_energy = np.mean(horizontal_edges[:, strip_start:strip_end])
            tilt_invariant.append(strip_energy)

        features.tilt_invariant_features = np.array(tilt_invariant)

    def _extract_perspective_corrected_features(self, gray_roi: np.ndarray, ptz_state: PTZState,
                                              features: PTZAwareMathematicalFeatures):
        """Extract features corrected for perspective distortion from tilt"""

        # Perspective correction based on tilt angle
        tilt_radians = ptz_state.tilt_angle * np.pi / 180.0

        if abs(tilt_radians) > 0.1:  # Significant tilt
            h, w = gray_roi.shape

            # Create perspective correction matrix
            # Simplified perspective correction for tilt
            correction_factor = np.cos(tilt_radians)

            # Apply perspective scaling
            corrected_h = int(h * correction_factor)
            if corrected_h > 10:  # Ensure minimum size
                corrected_roi = cv2.resize(gray_roi, (w, corrected_h))

                # Calculate energy on corrected image
                laplacian_corrected = cv2.Laplacian(corrected_roi, cv2.CV_64F)
                features.perspective_corrected_energy = np.var(laplacian_corrected) * (ptz_state.zoom_level ** 2)
            else:
                features.perspective_corrected_energy = features.laplacian_energy
        else:
            # No significant tilt, use original energy
            features.perspective_corrected_energy = features.laplacian_energy

    def _extract_advanced_ptz_features(self, roi: np.ndarray, gray_roi: np.ndarray,
                                      ptz_state: PTZState, features: PTZAwareMathematicalFeatures):
        """Extract advanced PTZ-aware features"""

        # PTZ motion consistency (how well motion matches expected PTZ behavior)
        if len(self.ptz_history) > 1:
            prev_state = self.ptz_history[-2]

            # Expected motion based on PTZ velocities
            expected_pan_change = prev_state.pan_velocity * (ptz_state.timestamp - prev_state.timestamp)
            expected_tilt_change = prev_state.tilt_velocity * (ptz_state.timestamp - prev_state.timestamp)

            actual_pan_change = ptz_state.pan_angle - prev_state.pan_angle
            actual_tilt_change = ptz_state.tilt_angle - prev_state.tilt_angle

            pan_consistency = 1.0 / (1.0 + abs(expected_pan_change - actual_pan_change))
            tilt_consistency = 1.0 / (1.0 + abs(expected_tilt_change - actual_tilt_change))

            features.ptz_motion_consistency = (pan_consistency + tilt_consistency) / 2.0
        else:
            features.ptz_motion_consistency = 1.0

        # Multi-scale PTZ features
        for scale in self.ptz_scales:
            # Adjust scale based on zoom level
            effective_scale = scale * ptz_state.zoom_level

            if effective_scale > 0.5:  # Valid scale
                sigma = effective_scale
                gaussian_filtered = filters.gaussian(gray_roi, sigma=sigma)
                ptz_energy = np.var(cv2.Laplacian(gaussian_filtered.astype(np.uint8), cv2.CV_64F))

                # Apply PTZ compensation
                ptz_compensation = self._calculate_ptz_compensation_factor(ptz_state)
                compensated_energy = ptz_energy * ptz_compensation

                features.multi_scale_ptz_features.append(compensated_energy)

        # Energy conservation checks
        if len(self.ptz_history) > 1:
            prev_state = self.ptz_history[-2]

            # Zoom energy conservation
            zoom_ratio = ptz_state.zoom_level / prev_state.zoom_level
            expected_energy_ratio = zoom_ratio ** 2

            if hasattr(features, 'previous_energy') and features.previous_energy > 0:
                actual_energy_ratio = features.ptz_compensated_energy / features.previous_energy
                features.zoom_energy_conservation = 1.0 / (1.0 + abs(expected_energy_ratio - actual_energy_ratio))
            else:
                features.zoom_energy_conservation = 1.0

            # Pan/Tilt energy conservation (should remain relatively stable)
            pan_change = abs(ptz_state.pan_angle - prev_state.pan_angle)
            tilt_change = abs(ptz_state.tilt_angle - prev_state.tilt_angle)

            if pan_change < 5.0 and tilt_change < 5.0:  # Small movements
                features.pan_tilt_energy_conservation = 1.0
            else:
                # Expect some energy variation with large movements
                movement_factor = min(1.0, (pan_change + tilt_change) / 20.0)
                features.pan_tilt_energy_conservation = 1.0 - movement_factor * 0.3
        else:
            features.zoom_energy_conservation = 1.0
            features.pan_tilt_energy_conservation = 1.0

        # PTZ-aware texture stability
        if hasattr(features, 'texture_energy') and features.texture_energy > 0:
            # Texture should be relatively stable across PTZ movements
            stability_factor = 1.0

            # Reduce stability expectation during fast movements
            if ptz_state.pan_velocity > 10.0 or ptz_state.tilt_velocity > 10.0:
                stability_factor = 0.7

            features.ptz_aware_texture_stability = features.texture_energy * stability_factor
        else:
            features.ptz_aware_texture_stability = 0.0

    def estimate_ptz_state_from_motion(self, frame: np.ndarray, previous_frame: np.ndarray,
                                      previous_ptz_state: PTZState) -> PTZState:
        """
        🎯 Estimate PTZ state from frame-to-frame motion analysis

        Useful when PTZ state is not directly available from camera
        """

        if previous_frame is None:
            return PTZState(timestamp=time.time())

        try:
            # Convert to grayscale
            gray_current = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            gray_previous = cv2.cvtColor(previous_frame, cv2.COLOR_BGR2GRAY)

            # Estimate global motion using feature matching
            orb = cv2.ORB_create()

            # Find keypoints and descriptors
            kp1, des1 = orb.detectAndCompute(gray_previous, None)
            kp2, des2 = orb.detectAndCompute(gray_current, None)

            if des1 is not None and des2 is not None and len(des1) > 10 and len(des2) > 10:
                # Match features
                bf = cv2.BFMatcher(cv2.NORM_HAMMING, crossCheck=True)
                matches = bf.match(des1, des2)
                matches = sorted(matches, key=lambda x: x.distance)

                if len(matches) > 10:
                    # Extract matched points
                    src_pts = np.float32([kp1[m.queryIdx].pt for m in matches[:20]]).reshape(-1, 1, 2)
                    dst_pts = np.float32([kp2[m.trainIdx].pt for m in matches[:20]]).reshape(-1, 1, 2)

                    # Estimate homography
                    H, mask = cv2.findHomography(src_pts, dst_pts, cv2.RANSAC, 5.0)

                    if H is not None:
                        # Decompose homography to estimate PTZ motion
                        h, w = frame.shape[:2]

                        # Estimate translation (pan/tilt)
                        translation_x = H[0, 2]
                        translation_y = H[1, 2]

                        # Convert to pan/tilt angles (simplified)
                        pan_change = -translation_x * previous_ptz_state.field_of_view_h / w
                        tilt_change = translation_y * previous_ptz_state.field_of_view_v / h

                        # Estimate zoom from scale change
                        scale_x = np.sqrt(H[0, 0]**2 + H[0, 1]**2)
                        scale_y = np.sqrt(H[1, 0]**2 + H[1, 1]**2)
                        scale_change = (scale_x + scale_y) / 2.0

                        # Create new PTZ state
                        new_ptz_state = PTZState(
                            pan_angle=previous_ptz_state.pan_angle + pan_change,
                            tilt_angle=previous_ptz_state.tilt_angle + tilt_change,
                            zoom_level=previous_ptz_state.zoom_level * scale_change,
                            pan_velocity=pan_change * 30.0,  # Assume 30 FPS
                            tilt_velocity=tilt_change * 30.0,
                            zoom_velocity=(scale_change - 1.0) * 30.0,
                            field_of_view_h=previous_ptz_state.field_of_view_h,
                            field_of_view_v=previous_ptz_state.field_of_view_v,
                            global_motion_vector=(translation_x, translation_y),
                            camera_shake_magnitude=np.sqrt(translation_x**2 + translation_y**2),
                            timestamp=time.time()
                        )

                        return new_ptz_state

        except Exception as e:
            logger.error(f"PTZ state estimation error: {e}")

        # Fallback: assume no change
        return PTZState(
            pan_angle=previous_ptz_state.pan_angle,
            tilt_angle=previous_ptz_state.tilt_angle,
            zoom_level=previous_ptz_state.zoom_level,
            field_of_view_h=previous_ptz_state.field_of_view_h,
            field_of_view_v=previous_ptz_state.field_of_view_v,
            timestamp=time.time()
        )