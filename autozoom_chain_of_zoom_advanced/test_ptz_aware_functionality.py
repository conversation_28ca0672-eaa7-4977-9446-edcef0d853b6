#!/usr/bin/env python3
"""
🧪 PTZ-Aware Functionality Test
==============================

Tests the PTZ-aware mathematical framework and tracking system:
- PTZ state estimation
- PTZ-compensated energy scaling
- Pan/tilt invariant features
- Global motion separation
- PTZ-aware tracking

Built with love for worker protection and safety! 💙
"""

import sys
import numpy as np
import cv2
from pathlib import Path
import logging
import time

# Add current directory to path
sys.path.append(str(Path(__file__).parent))

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_ptz_state_creation():
    """Test PTZ state creation and manipulation"""
    logger.info("🧪 Testing PTZ state creation...")
    
    try:
        from mathematics.ptz_aware_mathematical_extractor import PTZState
        
        # Create PTZ state
        ptz_state = PTZState(
            pan_angle=15.0,
            tilt_angle=-10.0,
            zoom_level=2.5,
            pan_velocity=5.0,
            tilt_velocity=-2.0,
            zoom_velocity=0.1,
            timestamp=time.time()
        )
        
        # Test basic properties
        assert ptz_state.pan_angle == 15.0
        assert ptz_state.tilt_angle == -10.0
        assert ptz_state.zoom_level == 2.5
        assert ptz_state.timestamp > 0
        
        logger.info("✅ PTZ state creation test passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ PTZ state creation test failed: {e}")
        return False

def test_ptz_aware_mathematical_extractor():
    """Test PTZ-aware mathematical feature extraction"""
    logger.info("🧪 Testing PTZ-aware mathematical extractor...")
    
    try:
        from mathematics.ptz_aware_mathematical_extractor import (
            PTZAwareMathematicalExtractor, PTZState, PTZAwareMathematicalFeatures
        )
        
        # Initialize extractor
        extractor = PTZAwareMathematicalExtractor()
        
        # Create test image and PTZ state
        test_image = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
        test_bbox = (100, 100, 300, 400)
        
        ptz_state = PTZState(
            pan_angle=10.0,
            tilt_angle=5.0,
            zoom_level=2.0,
            pan_velocity=1.0,
            tilt_velocity=0.5,
            timestamp=time.time()
        )
        
        # Extract PTZ-aware features
        features = extractor.extract_ptz_aware_features(test_image, test_bbox, ptz_state)
        
        # Test feature structure
        assert isinstance(features, PTZAwareMathematicalFeatures)
        assert features.ptz_compensated_energy > 0
        assert features.pan_invariant_features is not None
        assert features.tilt_invariant_features is not None
        assert len(features.pan_invariant_features) == 8
        assert len(features.tilt_invariant_features) == 8
        
        logger.info("✅ PTZ-aware mathematical extractor test passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ PTZ-aware mathematical extractor test failed: {e}")
        return False

def test_ptz_compensation_factor():
    """Test PTZ compensation factor calculation"""
    logger.info("🧪 Testing PTZ compensation factor...")
    
    try:
        from mathematics.ptz_aware_mathematical_extractor import (
            PTZAwareMathematicalExtractor, PTZState
        )
        
        extractor = PTZAwareMathematicalExtractor()
        
        # Test different PTZ states
        test_cases = [
            PTZState(pan_velocity=0.0, tilt_velocity=0.0, zoom_velocity=0.0),  # Stationary
            PTZState(pan_velocity=50.0, tilt_velocity=0.0, zoom_velocity=0.0),  # Fast pan
            PTZState(pan_velocity=0.0, tilt_velocity=30.0, zoom_velocity=0.0),  # Fast tilt
            PTZState(pan_velocity=10.0, tilt_velocity=5.0, zoom_velocity=0.2),  # Mixed motion
        ]
        
        for i, ptz_state in enumerate(test_cases):
            compensation = extractor._calculate_ptz_compensation_factor(ptz_state)
            
            # Compensation should be between 0.5 and 2.0
            assert 0.5 <= compensation <= 2.0, f"Case {i}: compensation {compensation} out of range"
            
            # Stationary case should have compensation close to 1.0
            if i == 0:
                assert 0.9 <= compensation <= 1.1, f"Stationary compensation should be ~1.0, got {compensation}"
        
        logger.info("✅ PTZ compensation factor test passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ PTZ compensation factor test failed: {e}")
        return False

def test_motion_separation():
    """Test global vs local motion separation"""
    logger.info("🧪 Testing motion separation...")
    
    try:
        from mathematics.ptz_aware_mathematical_extractor import (
            PTZAwareMathematicalExtractor, PTZState
        )
        
        extractor = PTZAwareMathematicalExtractor()
        
        # Create two test frames with simulated motion
        frame1 = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
        frame2 = frame1.copy()
        
        # Add global motion (camera pan)
        M = np.float32([[1, 0, 10], [0, 1, 5]])  # 10px right, 5px down
        frame2 = cv2.warpAffine(frame2, M, (640, 480))
        
        # Add local motion (object movement)
        cv2.rectangle(frame2, (200, 150), (250, 200), (255, 255, 255), -1)
        
        test_bbox = (180, 130, 270, 220)
        
        # PTZ states
        ptz_state1 = PTZState(pan_angle=0.0, tilt_angle=0.0, timestamp=0.0)
        ptz_state2 = PTZState(pan_angle=2.0, tilt_angle=1.0, timestamp=0.033)  # 30 FPS
        
        # Extract features
        features = extractor.extract_ptz_aware_features(
            frame2, test_bbox, ptz_state2, frame1, ptz_state1
        )
        
        # Should detect both global and local motion
        assert features.global_motion_magnitude >= 0
        assert features.local_motion_magnitude >= 0
        
        logger.info("✅ Motion separation test passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Motion separation test failed: {e}")
        return False

def test_ptz_state_estimation():
    """Test PTZ state estimation from frame motion"""
    logger.info("🧪 Testing PTZ state estimation...")
    
    try:
        from mathematics.ptz_aware_mathematical_extractor import (
            PTZAwareMathematicalExtractor, PTZState
        )
        
        extractor = PTZAwareMathematicalExtractor()
        
        # Create frames with known motion
        frame1 = np.random.randint(50, 200, (480, 640, 3), dtype=np.uint8)
        
        # Add some features for tracking
        for i in range(20):
            x, y = np.random.randint(50, 590), np.random.randint(50, 430)
            cv2.circle(frame1, (x, y), 5, (255, 255, 255), -1)
        
        # Create frame2 with pan motion
        M = np.float32([[1, 0, 20], [0, 1, 0]])  # 20px pan
        frame2 = cv2.warpAffine(frame1, M, (640, 480))
        
        previous_ptz_state = PTZState(
            pan_angle=0.0, tilt_angle=0.0, zoom_level=1.0,
            field_of_view_h=60.0, field_of_view_v=45.0
        )
        
        # Estimate new PTZ state
        estimated_ptz_state = extractor.estimate_ptz_state_from_motion(
            frame2, frame1, previous_ptz_state
        )
        
        # Should detect pan motion
        assert estimated_ptz_state.pan_angle != previous_ptz_state.pan_angle
        assert isinstance(estimated_ptz_state.timestamp, float)
        
        logger.info("✅ PTZ state estimation test passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ PTZ state estimation test failed: {e}")
        return False

def test_ptz_aware_tracker():
    """Test PTZ-aware tracker initialization"""
    logger.info("🧪 Testing PTZ-aware tracker...")
    
    try:
        from core.tracker_ptz_aware import PTZAwareEnergyTracker, PTZAwareTrack
        from core.detector_advanced import AdvancedDetection
        from mathematics.ptz_aware_mathematical_extractor import PTZState
        
        # Initialize tracker
        tracker = PTZAwareEnergyTracker()
        
        # Test basic properties
        assert hasattr(tracker, 'math_extractor')
        assert hasattr(tracker, 'tracks')
        assert hasattr(tracker, 'association_weights')
        
        # Test track creation
        test_detection = AdvancedDetection(
            bbox=(100, 100, 200, 200),
            confidence=0.8,
            class_name="person"
        )
        
        test_frame = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
        ptz_state = PTZState(zoom_level=1.5)
        
        # Update tracker
        tracks = tracker.update([test_detection], test_frame, ptz_state)
        
        # Should create new track
        assert len(tracker.tracks) == 1
        
        # Test performance stats
        stats = tracker.get_performance_stats()
        assert isinstance(stats, dict)
        assert 'tracking_fps' in stats
        
        logger.info("✅ PTZ-aware tracker test passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ PTZ-aware tracker test failed: {e}")
        return False

def test_energy_conservation():
    """Test energy conservation across PTZ operations"""
    logger.info("🧪 Testing energy conservation...")
    
    try:
        from mathematics.ptz_aware_mathematical_extractor import (
            PTZAwareMathematicalExtractor, PTZState
        )
        
        extractor = PTZAwareMathematicalExtractor()
        
        # Create test image
        test_image = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
        test_bbox = (100, 100, 300, 400)
        
        # Test energy scaling with zoom
        zoom_levels = [1.0, 1.5, 2.0, 2.5]
        energies = []
        
        for zoom in zoom_levels:
            ptz_state = PTZState(zoom_level=zoom)
            features = extractor.extract_ptz_aware_features(test_image, test_bbox, ptz_state)
            energies.append(features.ptz_compensated_energy)
        
        # Energy should scale with zoom² (approximately)
        for i in range(1, len(zoom_levels)):
            zoom_ratio = zoom_levels[i] / zoom_levels[0]
            energy_ratio = energies[i] / energies[0]
            expected_ratio = zoom_ratio ** 2
            
            # Allow some tolerance
            ratio_error = abs(energy_ratio - expected_ratio) / expected_ratio
            assert ratio_error < 0.5, f"Energy scaling error too large: {ratio_error}"
        
        logger.info("✅ Energy conservation test passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Energy conservation test failed: {e}")
        return False

def run_all_ptz_tests():
    """Run all PTZ-aware functionality tests"""
    logger.info("🚀 Starting PTZ-Aware Functionality Tests")
    logger.info("=" * 60)
    
    tests = [
        ("PTZ State Creation", test_ptz_state_creation),
        ("PTZ-Aware Mathematical Extractor", test_ptz_aware_mathematical_extractor),
        ("PTZ Compensation Factor", test_ptz_compensation_factor),
        ("Motion Separation", test_motion_separation),
        ("PTZ State Estimation", test_ptz_state_estimation),
        ("PTZ-Aware Tracker", test_ptz_aware_tracker),
        ("Energy Conservation", test_energy_conservation)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n🧪 Running {test_name} test...")
        try:
            results[test_name] = test_func()
        except Exception as e:
            logger.error(f"❌ {test_name} test crashed: {e}")
            results[test_name] = False
    
    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("📊 PTZ-AWARE TEST RESULTS SUMMARY")
    logger.info("=" * 60)
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"  {status} - {test_name}")
        if result:
            passed += 1
    
    logger.info(f"\nOverall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        logger.info("🎉 All PTZ-aware tests passed! System is bulletproof for PTZ movements.")
    else:
        logger.warning("⚠️ Some PTZ tests failed. Check logs above for details.")
    
    return passed == total

if __name__ == "__main__":
    success = run_all_ptz_tests()
    sys.exit(0 if success else 1)
