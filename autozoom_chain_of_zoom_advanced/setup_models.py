#!/usr/bin/env python3
"""
🛠️ AutoZoom Advanced Models Setup
=================================

Downloads and sets up models for advanced AutoZoom development:
- YOLOv8 helmet detection model (meryemsakin/helmet-detection-yolov8)
- Safety equipment detection models
- Re-ID feature extraction models

Built with love for worker protection and safety! 💙
"""

import os
import sys
import argparse
import logging
from pathlib import Path
import requests
from typing import Dict, List
import zipfile
import tarfile

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ModelDownloader:
    """
    🛠️ Model Download and Setup Manager
    
    Handles downloading and setting up all required models for advanced AutoZoom
    """
    
    def __init__(self):
        self.base_dir = Path(__file__).parent
        self.models_dir = self.base_dir / "models"
        
        # Create model directories
        self.helmet_dir = self.models_dir / "helmet_detector"
        self.safety_dir = self.models_dir / "safety_equipment"
        self.reid_dir = self.models_dir / "reid_features"
        
        for directory in [self.helmet_dir, self.safety_dir, self.reid_dir]:
            directory.mkdir(parents=True, exist_ok=True)
    
    def download_helmet_model(self):
        """Download YOLOv8 helmet detection model"""
        
        logger.info("🪖 Downloading helmet detection model...")
        
        # Model URLs (these would be actual URLs in production)
        model_urls = {
            "yolov8_helmet.pt": "https://github.com/meryemsakin/helmet-detection-yolov8/releases/download/v1.0/best.pt",
            # Fallback: Use Ultralytics hub model
            "yolov8n_helmet_fallback.pt": "https://ultralytics.com/models/yolov8n.pt"
        }
        
        success = False
        
        for model_name, url in model_urls.items():
            try:
                model_path = self.helmet_dir / model_name
                
                if model_path.exists():
                    logger.info(f"✅ {model_name} already exists")
                    success = True
                    continue
                
                logger.info(f"Downloading {model_name} from {url}")
                
                # Download with progress
                response = requests.get(url, stream=True)
                response.raise_for_status()
                
                total_size = int(response.headers.get('content-length', 0))
                downloaded = 0
                
                with open(model_path, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)
                            downloaded += len(chunk)
                            
                            if total_size > 0:
                                progress = (downloaded / total_size) * 100
                                print(f"\rProgress: {progress:.1f}%", end='', flush=True)
                
                print()  # New line after progress
                logger.info(f"✅ Downloaded {model_name}")
                success = True
                break
                
            except Exception as e:
                logger.warning(f"Failed to download {model_name}: {e}")
                continue
        
        if not success:
            logger.warning("⚠️ Could not download helmet model, will use fallback")
            self._create_helmet_model_fallback()
    
    def _create_helmet_model_fallback(self):
        """Create fallback helmet detection setup"""
        
        logger.info("Creating helmet detection fallback...")
        
        # Create a configuration file for fallback
        fallback_config = self.helmet_dir / "fallback_config.txt"
        with open(fallback_config, 'w') as f:
            f.write("# Helmet Detection Fallback Configuration\n")
            f.write("# Using YOLOv8 person detection with upper-body focus\n")
            f.write("model_type: yolov8_person_based\n")
            f.write("focus_region: upper_60_percent\n")
            f.write("confidence_threshold: 0.6\n")
        
        logger.info("✅ Helmet detection fallback configured")
    
    def download_safety_equipment_models(self):
        """Download additional safety equipment detection models"""
        
        logger.info("🦺 Setting up safety equipment detection models...")
        
        # Safety equipment model configurations
        safety_models = {
            "vest": {
                "url": "https://example.com/safety_vest_model.pt",
                "description": "Safety vest detection model"
            },
            "glasses": {
                "url": "https://example.com/safety_glasses_model.pt", 
                "description": "Safety glasses detection model"
            },
            "gloves": {
                "url": "https://example.com/safety_gloves_model.pt",
                "description": "Safety gloves detection model"
            }
        }
        
        for equipment, config in safety_models.items():
            model_path = self.safety_dir / f"yolov8_{equipment}.pt"
            
            if model_path.exists():
                logger.info(f"✅ {equipment} model already exists")
                continue
            
            logger.info(f"Setting up {equipment} detection...")
            
            # For now, create placeholder configurations
            # In production, these would be actual model downloads
            placeholder_config = self.safety_dir / f"{equipment}_config.txt"
            with open(placeholder_config, 'w') as f:
                f.write(f"# {config['description']}\n")
                f.write(f"model_type: yolov8_{equipment}\n")
                f.write(f"url: {config['url']}\n")
                f.write("status: placeholder\n")
            
            logger.info(f"✅ {equipment} detection configured (placeholder)")
    
    def download_reid_models(self):
        """Download Re-ID feature extraction models"""
        
        logger.info("🔍 Setting up Re-ID feature extraction models...")
        
        reid_models = {
            "resnet50_reid.pt": {
                "url": "https://example.com/resnet50_reid.pt",
                "description": "ResNet-50 based Re-ID model"
            },
            "efficientnet_reid.pt": {
                "url": "https://example.com/efficientnet_reid.pt",
                "description": "EfficientNet based Re-ID model"
            }
        }
        
        for model_name, config in reid_models.items():
            model_path = self.reid_dir / model_name
            
            if model_path.exists():
                logger.info(f"✅ {model_name} already exists")
                continue
            
            logger.info(f"Setting up {model_name}...")
            
            # Create placeholder configuration
            config_path = self.reid_dir / f"{model_name.replace('.pt', '_config.txt')}"
            with open(config_path, 'w') as f:
                f.write(f"# {config['description']}\n")
                f.write(f"model_name: {model_name}\n")
                f.write(f"url: {config['url']}\n")
                f.write("feature_dim: 2048\n")
                f.write("status: placeholder\n")
            
            logger.info(f"✅ {model_name} configured (placeholder)")
    
    def setup_base_models(self):
        """Setup base YOLOv8 models"""
        
        logger.info("📦 Setting up base YOLOv8 models...")
        
        try:
            from ultralytics import YOLO
            
            # Download base YOLOv8 models
            base_models = ["yolov8n.pt", "yolov8s.pt", "yolov8m.pt"]
            
            for model_name in base_models:
                logger.info(f"Checking {model_name}...")
                model = YOLO(model_name)  # This will download if not present
                logger.info(f"✅ {model_name} ready")
                
        except Exception as e:
            logger.error(f"Error setting up base models: {e}")
    
    def validate_models(self):
        """Validate that all models are properly set up"""
        
        logger.info("🔍 Validating model setup...")
        
        validation_results = {
            "helmet_detection": False,
            "safety_equipment": False,
            "reid_features": False,
            "base_models": False
        }
        
        # Check helmet detection
        helmet_files = list(self.helmet_dir.glob("*.pt")) + list(self.helmet_dir.glob("*config.txt"))
        validation_results["helmet_detection"] = len(helmet_files) > 0
        
        # Check safety equipment
        safety_files = list(self.safety_dir.glob("*config.txt"))
        validation_results["safety_equipment"] = len(safety_files) >= 3  # vest, glasses, gloves
        
        # Check Re-ID models
        reid_files = list(self.reid_dir.glob("*config.txt"))
        validation_results["reid_features"] = len(reid_files) > 0
        
        # Check base models
        try:
            from ultralytics import YOLO
            YOLO("yolov8n.pt")  # Test loading
            validation_results["base_models"] = True
        except:
            validation_results["base_models"] = False
        
        # Report results
        logger.info("📊 Validation Results:")
        for component, status in validation_results.items():
            status_icon = "✅" if status else "❌"
            logger.info(f"  {status_icon} {component}: {'OK' if status else 'FAILED'}")
        
        all_ok = all(validation_results.values())
        if all_ok:
            logger.info("🎉 All models validated successfully!")
        else:
            logger.warning("⚠️ Some models failed validation - check logs above")
        
        return all_ok
    
    def setup_all(self):
        """Setup all models and components"""
        
        logger.info("🚀 Starting complete model setup...")
        
        try:
            self.setup_base_models()
            self.download_helmet_model()
            self.download_safety_equipment_models()
            self.download_reid_models()
            
            success = self.validate_models()
            
            if success:
                logger.info("🎉 Model setup completed successfully!")
                logger.info("You can now run: python main_advanced.py --mode helmet_detection")
            else:
                logger.warning("⚠️ Model setup completed with warnings")
                
        except Exception as e:
            logger.error(f"❌ Model setup failed: {e}")
            return False
        
        return True

def main():
    """Main setup function"""
    
    parser = argparse.ArgumentParser(description="Setup AutoZoom Advanced Models")
    parser.add_argument("--download-helmet-model", action="store_true",
                       help="Download helmet detection model")
    parser.add_argument("--download-safety-models", action="store_true",
                       help="Download safety equipment models")
    parser.add_argument("--download-reid-models", action="store_true",
                       help="Download Re-ID models")
    parser.add_argument("--setup-all", action="store_true",
                       help="Setup all models and components")
    parser.add_argument("--validate", action="store_true",
                       help="Validate existing model setup")
    
    args = parser.parse_args()
    
    downloader = ModelDownloader()
    
    if args.download_helmet_model:
        downloader.download_helmet_model()
    elif args.download_safety_models:
        downloader.download_safety_equipment_models()
    elif args.download_reid_models:
        downloader.download_reid_models()
    elif args.validate:
        downloader.validate_models()
    elif args.setup_all:
        downloader.setup_all()
    else:
        # Default: setup all
        logger.info("No specific option provided, running full setup...")
        downloader.setup_all()

if __name__ == "__main__":
    main()
