"""
🎯 PTZ-Aware Energy-Based Tracker
================================

Advanced tracker that maintains ID consistency during full PTZ camera movements.
Bulletproof tracking system that accounts for pan, tilt, and zoom operations.

Core Innovation:
- PTZ-compensated energy scaling: E_ptz = E_base × zoom² × PTZ_compensation
- Global motion separation from object motion
- Pan/tilt invariant feature matching
- Camera shake filtering and stabilization

Built with love for worker protection and safety! 💙
"""

import cv2
import numpy as np
import time
from typing import List, Dict, Tuple, Optional, Any
from dataclasses import dataclass, field
from collections import defaultdict, deque
from scipy.optimize import linear_sum_assignment
from scipy.spatial.distance import cdist
import logging

# Import PTZ-aware mathematical framework
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

try:
    from mathematics.ptz_aware_mathematical_extractor import (
        PTZAwareMathematicalExtractor, PTZAwareMathematicalFeatures, PTZState
    )
    from core.detector_advanced import AdvancedDetection
except ImportError:
    # Fallback imports
    from ..mathematics.ptz_aware_mathematical_extractor import (
        PTZAwareMathematicalExtractor, PTZAwareMathematicalFeatures, PTZState
    )
    from .detector_advanced import AdvancedDetection

logger = logging.getLogger(__name__)

@dataclass
class PTZAwareTrack:
    """
    🎯 PTZ-Aware Track with enhanced features
    
    Maintains track state with PTZ-compensated features for robust ID preservation
    """
    # Core identification
    track_id: int
    
    # Spatial information (PTZ-compensated)
    bbox: Tuple[int, int, int, int]
    center: Tuple[float, float]
    
    # PTZ-aware energy features
    ptz_compensated_energy: float
    energy_history: deque = field(default_factory=lambda: deque(maxlen=20))
    
    # PTZ-invariant features
    pan_invariant_features: np.ndarray = field(default_factory=lambda: np.zeros(8))
    tilt_invariant_features: np.ndarray = field(default_factory=lambda: np.zeros(8))
    
    # Motion tracking (separated global vs local)
    global_motion_history: deque = field(default_factory=lambda: deque(maxlen=10))
    local_motion_history: deque = field(default_factory=lambda: deque(maxlen=10))
    
    # PTZ state tracking
    ptz_state_history: deque = field(default_factory=lambda: deque(maxlen=10))
    
    # Safety equipment tracking
    helmet_features: Optional[Dict[str, Any]] = None
    safety_equipment_history: deque = field(default_factory=lambda: deque(maxlen=5))
    
    # Track management
    age: int = 0
    hits: int = 0
    time_since_update: int = 0
    confirmed: bool = False
    
    # Performance tracking
    last_update_time: float = 0.0
    tracking_quality: float = 1.0

class PTZAwareEnergyTracker:
    """
    🎯 PTZ-Aware Energy-Based Tracker
    
    Advanced tracking system that maintains ID consistency during full PTZ movements:
    
    Key Features:
    1. **PTZ-Compensated Energy Scaling**: Maintains E ∝ zoom² regardless of camera movement
    2. **Global Motion Separation**: Distinguishes camera motion from object motion
    3. **Pan/Tilt Invariant Matching**: Features stable across pan/tilt operations
    4. **Camera Shake Filtering**: Robust to camera vibrations and instabilities
    5. **Multi-Scale PTZ Analysis**: Consistent tracking across all zoom levels
    """
    
    def __init__(self):
        """Initialize PTZ-aware tracker"""
        
        logger.info("🎯 Initializing PTZ-Aware Energy-Based Tracker...")
        
        # PTZ-aware mathematical extractor
        self.math_extractor = PTZAwareMathematicalExtractor()
        
        # Track management
        self.tracks = []
        self.next_track_id = 1
        self.max_track_age = 30
        self.min_track_hits = 3
        
        # PTZ state tracking
        self.current_ptz_state = PTZState()
        self.previous_ptz_state = None
        self.ptz_history = deque(maxlen=100)
        
        # Association parameters (PTZ-aware)
        self.association_weights = {
            'ptz_energy_similarity': 0.30,      # PTZ-compensated energy
            'pan_invariant_similarity': 0.20,   # Pan-invariant features
            'tilt_invariant_similarity': 0.20,  # Tilt-invariant features
            'spatial_proximity': 0.15,          # Spatial distance (PTZ-compensated)
            'motion_consistency': 0.10,         # Local motion consistency
            'safety_equipment_match': 0.05      # Helmet/equipment matching
        }
        
        # Performance tracking
        self.tracking_times = []
        self.id_switches = 0
        self.total_associations = 0
        
        logger.info("✅ PTZ-aware tracker initialized")
    
    def update(self, detections: List[AdvancedDetection], frame: np.ndarray, 
              ptz_state: Optional[PTZState] = None, previous_frame: Optional[np.ndarray] = None) -> List[PTZAwareTrack]:
        """
        🎯 Update tracker with new detections
        
        Args:
            detections: List of advanced detections
            frame: Current frame
            ptz_state: Current PTZ camera state (if available)
            previous_frame: Previous frame for motion analysis
        
        Returns:
            List of updated PTZ-aware tracks
        """
        start_time = time.time()
        
        try:
            # Update PTZ state
            if ptz_state is not None:
                self.previous_ptz_state = self.current_ptz_state
                self.current_ptz_state = ptz_state
            elif previous_frame is not None:
                # Estimate PTZ state from motion
                self.previous_ptz_state = self.current_ptz_state
                self.current_ptz_state = self.math_extractor.estimate_ptz_state_from_motion(
                    frame, previous_frame, self.current_ptz_state
                )
            
            self.ptz_history.append(self.current_ptz_state)
            
            # Extract PTZ-aware features for detections
            detection_features = []
            for detection in detections:
                features = self.math_extractor.extract_ptz_aware_features(
                    frame, detection.bbox, self.current_ptz_state, 
                    previous_frame, self.previous_ptz_state
                )
                detection_features.append(features)
            
            # Predict track positions (PTZ-compensated)
            self._predict_tracks()
            
            # Associate detections with tracks
            matched_tracks, unmatched_detections, unmatched_tracks = self._associate_detections(
                detections, detection_features
            )
            
            # Update matched tracks
            for track_idx, detection_idx in matched_tracks:
                self._update_track(
                    self.tracks[track_idx], detections[detection_idx], 
                    detection_features[detection_idx], frame
                )
            
            # Create new tracks for unmatched detections
            for detection_idx in unmatched_detections:
                self._create_new_track(
                    detections[detection_idx], detection_features[detection_idx], frame
                )
            
            # Handle unmatched tracks
            for track_idx in unmatched_tracks:
                self.tracks[track_idx].time_since_update += 1
            
            # Remove old tracks
            self.tracks = [track for track in self.tracks 
                          if track.time_since_update < self.max_track_age]
            
            # Performance tracking
            tracking_time = time.time() - start_time
            self.tracking_times.append(tracking_time)
            
            # Return confirmed tracks only
            confirmed_tracks = [track for track in self.tracks if track.confirmed]
            
            return confirmed_tracks
            
        except Exception as e:
            logger.error(f"PTZ-aware tracking error: {e}")
            return []
    
    def _predict_tracks(self):
        """Predict track positions accounting for PTZ motion"""
        
        if self.previous_ptz_state is None:
            return
        
        # Calculate global motion compensation
        pan_change = self.current_ptz_state.pan_angle - self.previous_ptz_state.pan_angle
        tilt_change = self.current_ptz_state.tilt_angle - self.previous_ptz_state.tilt_angle
        zoom_change = self.current_ptz_state.zoom_level / self.previous_ptz_state.zoom_level
        
        for track in self.tracks:
            if track.time_since_update == 0:  # Recently updated
                # Apply PTZ motion compensation to predicted position
                x1, y1, x2, y2 = track.bbox
                
                # Pan compensation (horizontal shift)
                pan_pixels = pan_change * (x2 - x1) / self.current_ptz_state.field_of_view_h
                
                # Tilt compensation (vertical shift)
                tilt_pixels = tilt_change * (y2 - y1) / self.current_ptz_state.field_of_view_v
                
                # Zoom compensation (scale change)
                center_x, center_y = track.center
                width, height = x2 - x1, y2 - y1
                
                new_width = width * zoom_change
                new_height = height * zoom_change
                
                # Update predicted bbox
                new_x1 = center_x - new_width / 2 + pan_pixels
                new_y1 = center_y - new_height / 2 + tilt_pixels
                new_x2 = center_x + new_width / 2 + pan_pixels
                new_y2 = center_y + new_height / 2 + tilt_pixels
                
                track.bbox = (int(new_x1), int(new_y1), int(new_x2), int(new_y2))
                track.center = ((new_x1 + new_x2) / 2, (new_y1 + new_y2) / 2)
    
    def _associate_detections(self, detections: List[AdvancedDetection], 
                            detection_features: List[PTZAwareMathematicalFeatures]) -> Tuple[List, List, List]:
        """Associate detections with tracks using PTZ-aware features"""
        
        if len(self.tracks) == 0:
            return [], list(range(len(detections))), []
        
        if len(detections) == 0:
            return [], [], list(range(len(self.tracks)))
        
        # Calculate association cost matrix
        cost_matrix = np.zeros((len(self.tracks), len(detections)))
        
        for i, track in enumerate(self.tracks):
            for j, (detection, features) in enumerate(zip(detections, detection_features)):
                cost_matrix[i, j] = self._calculate_ptz_aware_association_cost(
                    track, detection, features
                )
        
        # Hungarian algorithm for optimal assignment
        track_indices, detection_indices = linear_sum_assignment(cost_matrix)
        
        # Filter out high-cost associations
        matched_tracks = []
        for i, j in zip(track_indices, detection_indices):
            if cost_matrix[i, j] < 0.7:  # Association threshold
                matched_tracks.append((i, j))
        
        # Find unmatched detections and tracks
        matched_detection_indices = [j for _, j in matched_tracks]
        matched_track_indices = [i for i, _ in matched_tracks]
        
        unmatched_detections = [j for j in range(len(detections)) 
                              if j not in matched_detection_indices]
        unmatched_tracks = [i for i in range(len(self.tracks)) 
                           if i not in matched_track_indices]
        
        self.total_associations += len(matched_tracks)
        
        return matched_tracks, unmatched_detections, unmatched_tracks
    
    def _calculate_ptz_aware_association_cost(self, track: PTZAwareTrack, 
                                            detection: AdvancedDetection,
                                            features: PTZAwareMathematicalFeatures) -> float:
        """Calculate PTZ-aware association cost between track and detection"""
        
        total_cost = 0.0
        
        # 1. PTZ-compensated energy similarity
        if len(track.energy_history) > 0:
            track_energy = np.mean(list(track.energy_history))
            energy_diff = abs(track_energy - features.ptz_compensated_energy)
            energy_cost = min(1.0, energy_diff / max(track_energy, features.ptz_compensated_energy))
            total_cost += self.association_weights['ptz_energy_similarity'] * energy_cost
        
        # 2. Pan-invariant feature similarity
        if track.pan_invariant_features is not None and features.pan_invariant_features is not None:
            pan_similarity = np.corrcoef(track.pan_invariant_features, features.pan_invariant_features)[0, 1]
            pan_cost = 1.0 - max(0.0, pan_similarity)
            total_cost += self.association_weights['pan_invariant_similarity'] * pan_cost
        
        # 3. Tilt-invariant feature similarity
        if track.tilt_invariant_features is not None and features.tilt_invariant_features is not None:
            tilt_similarity = np.corrcoef(track.tilt_invariant_features, features.tilt_invariant_features)[0, 1]
            tilt_cost = 1.0 - max(0.0, tilt_similarity)
            total_cost += self.association_weights['tilt_invariant_similarity'] * tilt_cost
        
        # 4. Spatial proximity (PTZ-compensated)
        track_center = np.array(track.center)
        detection_center = np.array([(detection.bbox[0] + detection.bbox[2]) / 2,
                                   (detection.bbox[1] + detection.bbox[3]) / 2])
        
        spatial_distance = np.linalg.norm(track_center - detection_center)
        # Normalize by image diagonal (simplified)
        spatial_cost = min(1.0, spatial_distance / 1000.0)
        total_cost += self.association_weights['spatial_proximity'] * spatial_cost
        
        # 5. Motion consistency
        if len(track.local_motion_history) > 0:
            expected_motion = np.mean(list(track.local_motion_history))
            actual_motion = features.local_motion_magnitude
            motion_diff = abs(expected_motion - actual_motion)
            motion_cost = min(1.0, motion_diff / max(expected_motion, actual_motion, 1.0))
            total_cost += self.association_weights['motion_consistency'] * motion_cost
        
        # 6. Safety equipment matching
        if track.helmet_features and detection.safety_equipment:
            # Simple helmet matching (can be enhanced)
            helmet_match_cost = 0.0
            if 'helmet' in detection.safety_equipment:
                helmet_match_cost = 0.0  # Good match
            else:
                helmet_match_cost = 1.0  # No helmet detected
            
            total_cost += self.association_weights['safety_equipment_match'] * helmet_match_cost
        
        return total_cost
    
    def _update_track(self, track: PTZAwareTrack, detection: AdvancedDetection,
                     features: PTZAwareMathematicalFeatures, frame: np.ndarray):
        """Update track with new detection and PTZ-aware features"""
        
        # Update basic track info
        track.bbox = detection.bbox
        track.center = ((detection.bbox[0] + detection.bbox[2]) / 2,
                       (detection.bbox[1] + detection.bbox[3]) / 2)
        track.hits += 1
        track.time_since_update = 0
        track.last_update_time = time.time()
        
        # Update PTZ-aware features
        track.ptz_compensated_energy = features.ptz_compensated_energy
        track.energy_history.append(features.ptz_compensated_energy)
        
        track.pan_invariant_features = features.pan_invariant_features
        track.tilt_invariant_features = features.tilt_invariant_features
        
        # Update motion history
        track.global_motion_history.append(features.global_motion_magnitude)
        track.local_motion_history.append(features.local_motion_magnitude)
        
        # Update PTZ state history
        track.ptz_state_history.append(self.current_ptz_state)
        
        # Update safety equipment
        if detection.safety_equipment:
            track.helmet_features = detection.safety_equipment.get('helmet')
            track.safety_equipment_history.append(detection.safety_equipment)
        
        # Confirm track if enough hits
        if track.hits >= self.min_track_hits:
            track.confirmed = True
        
        # Update tracking quality
        track.tracking_quality = min(1.0, track.hits / 10.0)
    
    def _create_new_track(self, detection: AdvancedDetection, 
                         features: PTZAwareMathematicalFeatures, frame: np.ndarray):
        """Create new track from detection"""
        
        new_track = PTZAwareTrack(
            track_id=self.next_track_id,
            bbox=detection.bbox,
            center=((detection.bbox[0] + detection.bbox[2]) / 2,
                   (detection.bbox[1] + detection.bbox[3]) / 2),
            ptz_compensated_energy=features.ptz_compensated_energy,
            pan_invariant_features=features.pan_invariant_features,
            tilt_invariant_features=features.tilt_invariant_features,
            helmet_features=detection.safety_equipment.get('helmet') if detection.safety_equipment else None,
            hits=1,
            last_update_time=time.time()
        )
        
        # Initialize history
        new_track.energy_history.append(features.ptz_compensated_energy)
        new_track.global_motion_history.append(features.global_motion_magnitude)
        new_track.local_motion_history.append(features.local_motion_magnitude)
        new_track.ptz_state_history.append(self.current_ptz_state)
        
        if detection.safety_equipment:
            new_track.safety_equipment_history.append(detection.safety_equipment)
        
        self.tracks.append(new_track)
        self.next_track_id += 1
    
    def get_performance_stats(self) -> Dict[str, float]:
        """Get PTZ-aware tracking performance statistics"""
        
        if not self.tracking_times:
            return {}
        
        recent_times = self.tracking_times[-100:]
        
        stats = {
            'avg_tracking_time': np.mean(recent_times),
            'tracking_fps': 1.0 / np.mean(recent_times) if recent_times else 0,
            'total_tracks': len(self.tracks),
            'confirmed_tracks': sum(1 for track in self.tracks if track.confirmed),
            'id_switches': self.id_switches,
            'total_associations': self.total_associations,
            'id_switch_rate': self.id_switches / max(1, self.total_associations)
        }
        
        return stats
