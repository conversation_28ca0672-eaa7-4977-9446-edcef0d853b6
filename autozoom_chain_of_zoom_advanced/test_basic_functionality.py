#!/usr/bin/env python3
"""
🧪 Basic Functionality Test for AutoZoom Advanced
================================================

Tests core functionality of the advanced AutoZoom system:
- Configuration loading
- Detector initialization
- Mathematical feature extraction
- Basic processing pipeline

Built with love for worker protection and safety! 💙
"""

import sys
import numpy as np
import cv2
from pathlib import Path
import logging

# Add current directory to path
sys.path.append(str(Path(__file__).parent))

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_configuration():
    """Test configuration loading"""
    logger.info("🧪 Testing configuration loading...")
    
    try:
        from config_advanced import config_advanced, DetectionMode, TrackingMode, ZoomMode
        
        # Test basic configuration access
        assert config_advanced.detection_mode == DetectionMode.HELMET_DETECTION
        assert config_advanced.tracking_mode == TrackingMode.ENHANCED_ENERGY
        assert config_advanced.zoom_mode == ZoomMode.MULTI_PERSON
        
        # Test device detection
        device = config_advanced.get_device()
        assert device in ["cpu", "mps", "cuda"]
        
        # Test model path generation
        helmet_path = config_advanced.get_model_path("helmet")
        assert isinstance(helmet_path, str)
        
        logger.info("✅ Configuration test passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Configuration test failed: {e}")
        return False

def test_detector_initialization():
    """Test detector initialization"""
    logger.info("🧪 Testing detector initialization...")
    
    try:
        from core.detector_advanced import AdvancedPersonSafetyDetector
        
        # Initialize detector
        detector = AdvancedPersonSafetyDetector()
        
        # Test basic properties
        assert hasattr(detector, 'detection_mode')
        assert hasattr(detector, 'device')
        assert hasattr(detector, 'person_model')
        
        # Test performance stats
        stats = detector.get_performance_stats()
        assert isinstance(stats, dict)
        
        logger.info("✅ Detector initialization test passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Detector initialization test failed: {e}")
        return False

def test_mathematical_extractor():
    """Test mathematical feature extractor"""
    logger.info("🧪 Testing mathematical feature extractor...")
    
    try:
        from mathematics.advanced_mathematical_extractor import AdvancedMathematicalExtractor, MathematicalFeatures
        
        # Initialize extractor
        extractor = AdvancedMathematicalExtractor()
        
        # Create test image
        test_image = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
        test_bbox = (100, 100, 300, 400)
        
        # Extract features
        features = extractor.extract_features(test_image, test_bbox, zoom_level=2.0)
        
        # Test feature structure
        assert isinstance(features, MathematicalFeatures)
        assert features.laplacian_energy > 0
        assert features.gradient_magnitude_energy > 0
        assert len(features.gaussian_scale_space_energy) > 0
        
        # Test performance stats
        stats = extractor.get_performance_stats()
        assert isinstance(stats, dict)
        
        logger.info("✅ Mathematical extractor test passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Mathematical extractor test failed: {e}")
        return False

def test_detection_pipeline():
    """Test basic detection pipeline"""
    logger.info("🧪 Testing detection pipeline...")
    
    try:
        from core.detector_advanced import AdvancedPersonSafetyDetector
        
        # Initialize detector
        detector = AdvancedPersonSafetyDetector()
        
        # Create test frame
        test_frame = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
        
        # Add some structure to make detection more likely
        cv2.rectangle(test_frame, (200, 150), (400, 450), (100, 150, 200), -1)  # Person-like shape
        cv2.rectangle(test_frame, (250, 150), (350, 220), (50, 100, 150), -1)   # Head region
        
        # Run detection
        detections = detector.detect(test_frame)
        
        # Test detection structure
        assert isinstance(detections, list)
        
        # If detections found, test structure
        for detection in detections:
            assert hasattr(detection, 'bbox')
            assert hasattr(detection, 'confidence')
            assert hasattr(detection, 'class_name')
            assert hasattr(detection, 'safety_equipment')
            assert hasattr(detection, 'quality_score')
        
        logger.info(f"✅ Detection pipeline test passed ({len(detections)} detections)")
        return True
        
    except Exception as e:
        logger.error(f"❌ Detection pipeline test failed: {e}")
        return False

def test_main_demo_import():
    """Test main demo import"""
    logger.info("🧪 Testing main demo import...")
    
    try:
        from main_advanced import AdvancedAutoZoomDemo
        
        # Test demo initialization
        demo = AdvancedAutoZoomDemo(mode="helmet_detection")
        
        # Test basic properties
        assert hasattr(demo, 'detector')
        assert hasattr(demo, 'math_extractor')
        assert hasattr(demo, 'mode')
        
        logger.info("✅ Main demo import test passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Main demo import test failed: {e}")
        return False

def test_model_setup():
    """Test model setup functionality"""
    logger.info("🧪 Testing model setup...")
    
    try:
        from setup_models import ModelDownloader
        
        # Initialize downloader
        downloader = ModelDownloader()
        
        # Test directory creation
        assert downloader.helmet_dir.exists()
        assert downloader.safety_dir.exists()
        assert downloader.reid_dir.exists()
        
        # Test validation
        validation_results = downloader.validate_models()
        assert isinstance(validation_results, bool)
        
        logger.info("✅ Model setup test passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Model setup test failed: {e}")
        return False

def run_all_tests():
    """Run all basic functionality tests"""
    logger.info("🚀 Starting AutoZoom Advanced Basic Functionality Tests")
    logger.info("=" * 60)
    
    tests = [
        ("Configuration", test_configuration),
        ("Detector Initialization", test_detector_initialization),
        ("Mathematical Extractor", test_mathematical_extractor),
        ("Detection Pipeline", test_detection_pipeline),
        ("Main Demo Import", test_main_demo_import),
        ("Model Setup", test_model_setup)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n🧪 Running {test_name} test...")
        try:
            results[test_name] = test_func()
        except Exception as e:
            logger.error(f"❌ {test_name} test crashed: {e}")
            results[test_name] = False
    
    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("📊 TEST RESULTS SUMMARY")
    logger.info("=" * 60)
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"  {status} - {test_name}")
        if result:
            passed += 1
    
    logger.info(f"\nOverall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        logger.info("🎉 All tests passed! AutoZoom Advanced is ready for development.")
    else:
        logger.warning("⚠️ Some tests failed. Check logs above for details.")
    
    return passed == total

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
