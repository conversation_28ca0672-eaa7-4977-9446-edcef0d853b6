"""
⚙️ AutoZoom Consolidated Demo Configuration
==========================================

Configuration settings for the life-saving AutoZoom demo system.
Built with love for worker protection and safety! 💙
"""

import os
import torch
from dataclasses import dataclass
from typing import Tuple, Dict, Any

@dataclass
class AutoZoomConfig:
    """
    🎯 Core AutoZoom Configuration
    
    Based on your original AutoZoom requirements and technical philosophy:
    - Real data only, no mock dependencies
    - Scientific rigor with evidence-based validation
    - Confidence-based zoom triggering for safety
    """
    
    # ============================================================================
    # 🔍 DETECTION CONFIGURATION
    # ============================================================================
    
    # Person Detection
    person_model: str = "yolov8n.pt"           # Lightweight YOLO for M3 Max
    person_confidence: float = 0.5             # Person detection threshold
    person_iou: float = 0.45                   # Non-max suppression IoU
    
    # Proxy Feature Detection (Face as Helmet Proxy)
    proxy_feature: str = "face"                # Use face detection as helmet proxy
    face_cascade_path: str = "haarcascade_frontalface_default.xml"
    face_scale_factor: float = 1.1             # Face detection scale factor
    face_min_neighbors: int = 5                # Face detection min neighbors
    face_min_size: Tuple[int, int] = (30, 30)  # Minimum face size
    
    # ============================================================================
    # 🎯 TRACKING CONFIGURATION  
    # ============================================================================
    
    # Energy-Based Tracking (Your Laplacian Energy Innovation)
    enable_energy_scaling: bool = True         # Enable E ∝ zoom² scaling
    laplacian_kernel_size: int = 3             # Laplacian kernel size
    energy_smoothing_alpha: float = 0.7        # Temporal energy smoothing
    
    # Track ID Management
    max_track_age: int = 30                    # Max frames without detection
    min_track_hits: int = 3                    # Min hits before confirmed track
    track_id_preservation: bool = True         # Preserve IDs during zoom
    
    # ============================================================================
    # 📊 CONFIDENCE MONITORING
    # ============================================================================
    
    # Confidence Thresholds
    confidence_threshold: float = 0.5          # Trigger zoom below this
    confidence_recovery_threshold: float = 0.7  # Consider recovered above this
    confidence_smoothing_window: int = 5       # Frames for confidence smoothing
    
    # Occlusion Simulation (for demo purposes)
    simulate_occlusion: bool = True            # Simulate confidence drops
    occlusion_probability: float = 0.005       # Reduced probability per frame (was 0.02)
    occlusion_duration_range: Tuple[int, int] = (15, 45)  # Shorter duration in frames
    
    # ============================================================================
    # 🔍 AUTOZOOM LOGIC
    # ============================================================================
    
    # Zoom Triggering
    zoom_scale: float = 2.0                    # 2x zoom magnification
    max_zoom_duration: float = 2.0             # Maximum zoom duration (seconds)
    zoom_cooldown: float = 1.0                 # Cooldown between zooms (seconds)
    
    # Zoom Decision Logic
    min_streak_length: int = 5                 # Min low-confidence streak (increased from 3)
    zoom_timeout_seconds: int = 30             # Max time in zoom mode
    
    # ============================================================================
    # 📹 ZOOM SIMULATION
    # ============================================================================
    
    # PTZ Simulation Parameters
    zoom_transition_frames: int = 15           # Frames for smooth zoom transition
    zoom_center_smoothing: float = 0.8         # Center position smoothing
    maintain_aspect_ratio: bool = True         # Keep original aspect ratio
    
    # Crop Parameters
    min_crop_size: Tuple[int, int] = (320, 240)  # Minimum crop dimensions
    crop_padding: float = 0.2                  # Extra padding around target
    
    # ============================================================================
    # 🎨 VISUALIZATION
    # ============================================================================
    
    # Display Settings
    display_width: int = 1280                  # Display window width
    display_height: int = 720                  # Display window height
    show_debug_info: bool = True               # Show debug overlays
    
    # Colors (BGR format for OpenCV)
    color_person_box: Tuple[int, int, int] = (0, 255, 0)      # Green
    color_face_box: Tuple[int, int, int] = (255, 0, 0)        # Blue  
    color_low_confidence: Tuple[int, int, int] = (0, 0, 255)   # Red
    color_zoom_indicator: Tuple[int, int, int] = (0, 255, 255) # Yellow
    color_text: Tuple[int, int, int] = (255, 255, 255)        # White
    
    # Text Settings
    font_scale: float = 0.6
    font_thickness: int = 2
    
    # ============================================================================
    # 🚀 PERFORMANCE
    # ============================================================================
    
    # Processing Settings
    target_fps: int = 25                       # Target output FPS
    max_processing_time: float = 0.04          # Max time per frame (25 FPS)
    
    # M3 Max Optimization
    use_mps: bool = True                       # Use Apple Metal Performance Shaders
    batch_processing: bool = False             # Disable batching for real-time
    
    # Memory Management
    max_frame_history: int = 100               # Max frames to keep in memory
    cleanup_interval: int = 50                 # Cleanup every N frames
    
    # ============================================================================
    # 📁 FILE PATHS
    # ============================================================================
    
    # Input/Output
    default_video_path: str = "videos/test_input.mp4"
    default_output_path: str = "results/zoomed_output.mp4"
    results_dir: str = "results"
    
    # Model Paths
    models_dir: str = "models"
    
    def __post_init__(self):
        """Initialize derived settings and validate configuration"""
        
        # Create directories if they don't exist
        os.makedirs(self.results_dir, exist_ok=True)
        os.makedirs(self.models_dir, exist_ok=True)
        
        # Validate critical parameters
        assert 0.0 < self.confidence_threshold < 1.0, "Confidence threshold must be between 0 and 1"
        assert self.zoom_scale > 1.0, "Zoom scale must be greater than 1.0"
        assert self.target_fps > 0, "Target FPS must be positive"
        
        # Calculate derived values
        self.max_zoom_frames = int(self.max_zoom_duration * self.target_fps)
        self.zoom_cooldown_frames = int(self.zoom_cooldown * self.target_fps)
        
    def get_device(self) -> str:
        """Get the optimal device for processing"""
        if self.use_mps and hasattr(torch, 'backends') and torch.backends.mps.is_available():
            return "mps"
        elif torch.cuda.is_available():
            return "cuda"
        else:
            return "cpu"

# Global configuration instance
config = AutoZoomConfig()
