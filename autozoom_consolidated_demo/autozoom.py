"""
🔍 AutoZoom Logic Controller
===========================

Implements zoom trigger logic based on confidence thresholds and coordinates
with zoom_simulator for actual zoom operations. Core of the AutoZoom system.

Logic Flow:
1. Receive confidence recommendations from monitor
2. Select optimal zoom target based on priority
3. Coordinate with zoom simulator for smooth transitions
4. Track zoom state and recovery progress

Built with love for worker protection and safety! 💙
"""

import numpy as np
import time
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from enum import Enum

from config import config
from tracker import Track
from confidence_monitor import ConfidenceRecord, ConfidenceState

class ZoomState(Enum):
    """AutoZoom system state"""
    NORMAL = "normal"           # Normal view, no zoom
    ZOOMING_IN = "zooming_in"   # Transitioning to zoom
    ZOOMED = "zoomed"           # Actively zoomed on target
    ZOOMING_OUT = "zooming_out" # Transitioning back to normal
    COOLDOWN = "cooldown"       # Cooldown period after zoom

@dataclass
class ZoomTarget:
    """
    🎯 Zoom target information
    
    Represents a track that should be zoomed on
    """
    track_id: int
    priority: float
    bbox: Tuple[int, int, int, int]
    confidence: float
    confidence_state: ConfidenceState
    streak_length: int
    
    # Target center for zoom
    center: Tuple[float, float]
    
    # Timing information
    trigger_time: float
    last_seen: float

class AutoZoomController:
    """
    🔍 AutoZoom Logic Controller
    
    Manages the AutoZoom system state and coordinates zoom operations
    based on confidence monitoring recommendations.
    
    Key Features:
    1. Priority-based target selection
    2. Smooth zoom transitions
    3. Recovery tracking
    4. Cooldown management
    5. Performance optimization
    """
    
    def __init__(self):
        """Initialize AutoZoom controller"""
        
        print("🔍 Initializing AutoZoom Controller...")
        
        # System state
        self.zoom_state = ZoomState.NORMAL
        self.current_zoom_level = 1.0
        self.target_zoom_level = 1.0
        
        # Current zoom target
        self.current_target: Optional[ZoomTarget] = None
        self.zoom_start_time: Optional[float] = None
        self.zoom_duration = 0.0
        
        # Zoom history and statistics
        self.zoom_operations = 0
        self.successful_zooms = 0
        self.zoom_history = []
        
        # Performance tracking
        self.processing_times = []
        self.frame_count = 0
        
        print("✅ AutoZoom Controller initialized")
    
    def calculate_target_priority(self, track: Track, confidence_record: ConfidenceRecord) -> float:
        """
        🎯 Calculate priority score for zoom target selection with face preference

        Higher priority = more urgent need for zoom

        Args:
            track: Track information
            confidence_record: Confidence record

        Returns:
            Priority score (higher = more urgent)
        """
        priority = 0.0

        # Face presence bonus (CRITICAL for zoom targeting)
        if track.has_proxy_feature and track.proxy_bbox:
            priority += 0.3  # Strong bonus for having a face

            # Face quality bonus
            face_area = (track.proxy_bbox[2] - track.proxy_bbox[0]) * (track.proxy_bbox[3] - track.proxy_bbox[1])
            person_area = (track.bbox[2] - track.bbox[0]) * (track.bbox[3] - track.bbox[1])

            if person_area > 0:
                face_ratio = face_area / person_area
                if 0.02 < face_ratio < 0.15:  # Good face size ratio
                    priority += 0.2
        else:
            # Penalty for no face (harder to zoom effectively)
            priority -= 0.2

        # Confidence-based priority (lower confidence = higher priority)
        confidence_factor = 1.0 - confidence_record.smoothed_confidence
        priority += confidence_factor * 0.3  # Reduced from 0.4

        # Streak length priority (longer streak = higher priority)
        max_streak = max(10, confidence_record.max_streak_length)
        streak_factor = confidence_record.low_confidence_streak / max_streak
        priority += streak_factor * 0.2  # Reduced from 0.3

        # Critical state bonus
        if confidence_record.confidence_state == ConfidenceState.CRITICAL:
            priority += 0.15  # Reduced from 0.2

        # Track stability (more stable tracks get priority)
        if track.hits > 10:
            priority += 0.05  # Reduced from 0.1

        return priority
    
    def select_zoom_target(self, tracks: List[Track], confidence_records: Dict[int, ConfidenceRecord]) -> Optional[ZoomTarget]:
        """
        🎯 Select optimal zoom target from available candidates
        
        Args:
            tracks: List of available tracks
            confidence_records: Confidence records for tracks
            
        Returns:
            Selected zoom target or None
        """
        candidates = []
        
        for track in tracks:
            record = confidence_records.get(track.track_id)
            if not record or not record.zoom_recommended:
                continue
            
            # Calculate priority
            priority = self.calculate_target_priority(track, record)
            
            # Create target candidate with face-centered zoom
            if track.has_proxy_feature and track.proxy_bbox:
                # Use face center for more precise zoom
                face_center = (
                    (track.proxy_bbox[0] + track.proxy_bbox[2]) / 2,
                    (track.proxy_bbox[1] + track.proxy_bbox[3]) / 2
                )
                zoom_bbox = track.proxy_bbox  # Zoom to face region
            else:
                # Fallback to person center
                face_center = track.center
                zoom_bbox = track.bbox

            target = ZoomTarget(
                track_id=track.track_id,
                priority=priority,
                bbox=zoom_bbox,  # Use face bbox if available
                confidence=record.smoothed_confidence,
                confidence_state=record.confidence_state,
                streak_length=record.low_confidence_streak,
                center=face_center,  # Use face center for zoom
                trigger_time=record.zoom_triggered_time or time.time(),
                last_seen=track.last_seen
            )
            
            candidates.append(target)
        
        if not candidates:
            return None
        
        # Select highest priority target
        candidates.sort(key=lambda x: x.priority, reverse=True)
        return candidates[0]
    
    def should_start_zoom(self, zoom_target: ZoomTarget) -> bool:
        """
        🚀 Determine if zoom operation should start
        
        Args:
            zoom_target: Proposed zoom target
            
        Returns:
            True if zoom should start
        """
        # Check system state
        if self.zoom_state != ZoomState.NORMAL:
            return False
        
        # Check cooldown
        if self.zoom_history:
            last_zoom_end = self.zoom_history[-1].get('end_time', 0)
            if time.time() - last_zoom_end < config.zoom_cooldown:
                return False
        
        # Check target validity
        if zoom_target.priority < 0.3:  # Minimum priority threshold
            return False
        
        return True
    
    def should_end_zoom(self, confidence_records: Dict[int, ConfidenceRecord]) -> bool:
        """
        🔚 Determine if current zoom should end
        
        Args:
            confidence_records: Current confidence records
            
        Returns:
            True if zoom should end
        """
        if not self.current_target:
            return True
        
        # Check maximum zoom duration
        if self.zoom_start_time:
            zoom_duration = time.time() - self.zoom_start_time
            if zoom_duration > config.max_zoom_duration:
                return True
        
        # Check if target recovered
        record = confidence_records.get(self.current_target.track_id)
        if record:
            if (record.confidence_state == ConfidenceState.HIGH and 
                record.smoothed_confidence >= config.confidence_recovery_threshold):
                return True
        
        # Check if target lost
        if not record or record.last_updated < time.time() - 2.0:
            return True
        
        return False
    
    def update_zoom_state(self, tracks: List[Track], confidence_records: Dict[int, ConfidenceRecord]) -> Tuple[ZoomState, float, Optional[ZoomTarget]]:
        """
        🔄 Update AutoZoom system state
        
        Args:
            tracks: Current tracks
            confidence_records: Confidence records
            
        Returns:
            (new_state, zoom_level, zoom_target)
        """
        current_time = time.time()
        
        if self.zoom_state == ZoomState.NORMAL:
            # Look for zoom targets
            zoom_target = self.select_zoom_target(tracks, confidence_records)
            
            if zoom_target and self.should_start_zoom(zoom_target):
                # Start zoom operation
                self.zoom_state = ZoomState.ZOOMING_IN
                self.current_target = zoom_target
                self.zoom_start_time = current_time
                self.target_zoom_level = config.zoom_scale
                self.zoom_operations += 1
                
                print(f"🔍 Starting zoom on track {zoom_target.track_id} (priority: {zoom_target.priority:.2f})")
        
        elif self.zoom_state == ZoomState.ZOOMING_IN:
            # Smooth transition to target zoom level
            zoom_progress = min(1.0, (current_time - self.zoom_start_time) / 0.5)  # 0.5s transition
            self.current_zoom_level = 1.0 + (self.target_zoom_level - 1.0) * zoom_progress
            
            if zoom_progress >= 1.0:
                self.zoom_state = ZoomState.ZOOMED
        
        elif self.zoom_state == ZoomState.ZOOMED:
            # Check if zoom should end
            if self.should_end_zoom(confidence_records):
                self.zoom_state = ZoomState.ZOOMING_OUT
                self.target_zoom_level = 1.0
                
                # Record zoom completion
                if self.current_target:
                    record = confidence_records.get(self.current_target.track_id)
                    if record and record.confidence_state == ConfidenceState.HIGH:
                        self.successful_zooms += 1
                
                print(f"🔍 Ending zoom on track {self.current_target.track_id if self.current_target else 'unknown'}")
        
        elif self.zoom_state == ZoomState.ZOOMING_OUT:
            # Smooth transition back to normal
            zoom_progress = min(1.0, (current_time - self.zoom_start_time) / 0.5)  # 0.5s transition
            self.current_zoom_level = config.zoom_scale + (1.0 - config.zoom_scale) * zoom_progress
            
            if zoom_progress >= 1.0:
                self.zoom_state = ZoomState.COOLDOWN
                self.current_zoom_level = 1.0
                
                # Record zoom history
                if self.zoom_start_time:
                    self.zoom_history.append({
                        'target_id': self.current_target.track_id if self.current_target else None,
                        'start_time': self.zoom_start_time,
                        'end_time': current_time,
                        'duration': current_time - self.zoom_start_time,
                        'successful': self.current_target.track_id in [r.track_id for r in confidence_records.values() if r.confidence_state == ConfidenceState.HIGH] if self.current_target else False
                    })
                
                self.current_target = None
                self.zoom_start_time = None
        
        elif self.zoom_state == ZoomState.COOLDOWN:
            # Wait for cooldown period
            if self.zoom_history and current_time - self.zoom_history[-1]['end_time'] >= config.zoom_cooldown:
                self.zoom_state = ZoomState.NORMAL
        
        return self.zoom_state, self.current_zoom_level, self.current_target
    
    def update(self, tracks: List[Track], confidence_records: Dict[int, ConfidenceRecord]) -> Dict[str, Any]:
        """
        🚀 Main AutoZoom update function
        
        Args:
            tracks: Current tracks
            confidence_records: Confidence records
            
        Returns:
            AutoZoom status and commands
        """
        start_time = time.time()
        self.frame_count += 1
        
        # Update zoom state
        new_state, zoom_level, zoom_target = self.update_zoom_state(tracks, confidence_records)
        
        # Prepare result
        result = {
            'zoom_state': new_state,
            'zoom_level': zoom_level,
            'zoom_target': zoom_target,
            'zoom_center': zoom_target.center if zoom_target else None,
            'zoom_bbox': zoom_target.bbox if zoom_target else None,
            'is_zooming': new_state in [ZoomState.ZOOMING_IN, ZoomState.ZOOMED, ZoomState.ZOOMING_OUT],
            'zoom_progress': self._calculate_zoom_progress()
        }
        
        # Track performance
        processing_time = time.time() - start_time
        self.processing_times.append(processing_time)
        
        if len(self.processing_times) > 100:
            self.processing_times = self.processing_times[-100:]
        
        return result
    
    def _calculate_zoom_progress(self) -> float:
        """Calculate zoom transition progress (0.0 to 1.0)"""
        if self.zoom_state == ZoomState.NORMAL:
            return 0.0
        elif self.zoom_state == ZoomState.ZOOMED:
            return 1.0
        elif self.zoom_state in [ZoomState.ZOOMING_IN, ZoomState.ZOOMING_OUT]:
            if self.zoom_start_time:
                return min(1.0, (time.time() - self.zoom_start_time) / 0.5)
        return 0.0
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get AutoZoom performance statistics"""
        if not self.processing_times:
            return {
                "avg_time": 0.0,
                "fps": 0.0,
                "zoom_operations": 0,
                "successful_zooms": 0,
                "success_rate": 0.0
            }
        
        avg_time = np.mean(self.processing_times)
        fps = 1.0 / avg_time if avg_time > 0 else 0.0
        success_rate = (self.successful_zooms / self.zoom_operations 
                       if self.zoom_operations > 0 else 0.0)
        
        return {
            "avg_time": avg_time,
            "fps": fps,
            "zoom_operations": self.zoom_operations,
            "successful_zooms": self.successful_zooms,
            "success_rate": success_rate,
            "current_state": self.zoom_state.value,
            "current_zoom_level": self.current_zoom_level
        }
