"""
🔍 Person and Proxy Feature Detector
====================================

Detects persons and proxy safety features (face as helmet proxy) using
lightweight models optimized for M3 Max performance.

Core Innovation:
- Uses face detection as proxy for helmet detection
- Lightweight YOLOv8 for person detection
- Real data only - no mock dependencies
- Optimized for real-time performance

Built with love for worker protection and safety! 💙
"""

import cv2
import numpy as np
import torch
from ultralytics import Y<PERSON><PERSON>
from typing import List, Dict, Tuple, Optional, Any
from dataclasses import dataclass
import time
import os

from config import config

@dataclass
class Detection:
    """
    🎯 Single detection result
    
    Represents a detected person with optional proxy feature (face)
    """
    # Bounding box (x1, y1, x2, y2)
    bbox: Tuple[int, int, int, int]
    
    # Detection confidence
    confidence: float
    
    # Detection class
    class_name: str
    
    # Proxy feature info (face detection)
    has_proxy_feature: bool = False
    proxy_bbox: Optional[Tuple[int, int, int, int]] = None
    proxy_confidence: float = 0.0
    
    # Additional metadata
    detection_time: float = 0.0
    frame_id: int = 0

class PersonProxyDetector:
    """
    🔍 Person and Proxy Feature Detector
    
    Combines person detection (YOLOv8) with proxy feature detection (face)
    to simulate helmet detection for safety monitoring.
    
    Philosophy: Real data only, scientifically rigorous, optimized for life-saving applications
    """
    
    def __init__(self):
        """Initialize detector with optimized models"""
        
        print("🔍 Initializing Person + Proxy Feature Detector...")
        
        # Initialize person detector (YOLOv8)
        self._init_person_detector()
        
        # Initialize proxy feature detector (face detection)
        self._init_proxy_detector()
        
        # Performance tracking
        self.detection_times = []
        self.frame_count = 0
        
        print(f"✅ Detector initialized on device: {config.get_device()}")
    
    def _init_person_detector(self):
        """Initialize YOLOv8 person detector"""
        try:
            # Load YOLOv8 model
            model_path = os.path.join(config.models_dir, config.person_model)
            
            if not os.path.exists(model_path):
                print(f"📥 Downloading {config.person_model}...")
                
            self.person_model = YOLO(config.person_model)
            
            # Set device
            device = config.get_device()
            if device != "cpu":
                self.person_model.to(device)
                
            print(f"✅ Person detector loaded: {config.person_model}")
            
        except Exception as e:
            print(f"❌ Failed to load person detector: {e}")
            raise
    
    def _init_proxy_detector(self):
        """Initialize face detector as helmet proxy"""
        try:
            # Load Haar cascade for face detection
            cascade_path = cv2.data.haarcascades + config.face_cascade_path
            
            if not os.path.exists(cascade_path):
                raise FileNotFoundError(f"Face cascade not found: {cascade_path}")
                
            self.face_cascade = cv2.CascadeClassifier(cascade_path)
            
            if self.face_cascade.empty():
                raise ValueError("Failed to load face cascade classifier")
                
            print("✅ Face detector loaded as helmet proxy")
            
        except Exception as e:
            print(f"❌ Failed to load proxy detector: {e}")
            raise
    
    def detect_persons(self, frame: np.ndarray) -> List[Detection]:
        """
        🎯 Detect persons in frame using YOLOv8
        
        Args:
            frame: Input frame (BGR format)
            
        Returns:
            List of person detections
        """
        start_time = time.time()
        
        try:
            # Run YOLO inference
            results = self.person_model(
                frame,
                conf=config.person_confidence,
                iou=config.person_iou,
                classes=[0],  # Person class only
                verbose=False
            )
            
            detections = []
            
            if results and len(results) > 0:
                result = results[0]
                
                if result.boxes is not None:
                    boxes = result.boxes.xyxy.cpu().numpy()
                    confidences = result.boxes.conf.cpu().numpy()
                    
                    for box, conf in zip(boxes, confidences):
                        x1, y1, x2, y2 = map(int, box)
                        
                        detection = Detection(
                            bbox=(x1, y1, x2, y2),
                            confidence=float(conf),
                            class_name="person",
                            detection_time=time.time() - start_time,
                            frame_id=self.frame_count
                        )
                        
                        detections.append(detection)
            
            return detections
            
        except Exception as e:
            print(f"❌ Person detection failed: {e}")
            return []
    
    def detect_proxy_features(self, frame: np.ndarray, person_bbox: Tuple[int, int, int, int]) -> Tuple[bool, Optional[Tuple[int, int, int, int]], float]:
        """
        👤 Enhanced face detection within person bounding box for zoom targeting

        Args:
            frame: Input frame (BGR format)
            person_bbox: Person bounding box (x1, y1, x2, y2)

        Returns:
            (has_feature, feature_bbox, confidence)
        """
        try:
            x1, y1, x2, y2 = person_bbox

            # Extract person ROI with padding for better face detection
            padding = 10
            padded_x1 = max(0, x1 - padding)
            padded_y1 = max(0, y1 - padding)
            padded_x2 = min(frame.shape[1], x2 + padding)
            padded_y2 = min(frame.shape[0], y2 + padding)

            person_roi = frame[padded_y1:padded_y2, padded_x1:padded_x2]

            if person_roi.size == 0:
                return False, None, 0.0

            # Convert to grayscale for face detection
            gray_roi = cv2.cvtColor(person_roi, cv2.COLOR_BGR2GRAY)

            # Enhanced face detection with multiple scales
            faces = self.face_cascade.detectMultiScale(
                gray_roi,
                scaleFactor=config.face_scale_factor,
                minNeighbors=config.face_min_neighbors,
                minSize=config.face_min_size,
                flags=cv2.CASCADE_SCALE_IMAGE
            )

            if len(faces) > 0:
                # Find the best face (largest + most centered in upper portion)
                best_face = None
                best_score = 0.0

                person_height = y2 - y1
                person_width = x2 - x1

                for fx, fy, fw, fh in faces:
                    # Convert to global coordinates
                    global_fx = padded_x1 + fx
                    global_fy = padded_y1 + fy

                    # Check if face is in upper portion of person (head region)
                    relative_y = (global_fy - y1) / person_height if person_height > 0 else 0
                    if relative_y > 0.4:  # Face should be in upper 40% of person
                        continue

                    # Calculate face quality score
                    face_area = fw * fh
                    person_area = person_width * person_height
                    size_score = min(1.0, face_area / (person_area * 0.05))  # 5% of person area

                    # Position score (prefer centered faces)
                    face_center_x = global_fx + fw // 2
                    person_center_x = (x1 + x2) // 2
                    center_distance = abs(face_center_x - person_center_x) / person_width
                    position_score = max(0.0, 1.0 - center_distance)

                    # Combined score
                    total_score = 0.6 * size_score + 0.4 * position_score

                    if total_score > best_score:
                        best_score = total_score
                        best_face = (global_fx, global_fy, global_fx + fw, global_fy + fh)

                if best_face is not None:
                    # Enhanced confidence calculation
                    fx1, fy1, fx2, fy2 = best_face
                    face_area = (fx2 - fx1) * (fy2 - fy1)
                    person_area = (x2 - x1) * (y2 - y1)

                    # Base confidence from face size
                    size_confidence = min(1.0, face_area / (person_area * 0.03))

                    # Quality confidence from face position
                    face_y_ratio = (fy1 - y1) / (y2 - y1) if (y2 - y1) > 0 else 0
                    position_confidence = max(0.3, 1.0 - face_y_ratio * 2.5)  # Prefer upper faces

                    # Final confidence
                    final_confidence = min(1.0, 0.7 * size_confidence + 0.3 * position_confidence)

                    return True, best_face, final_confidence

            return False, None, 0.0

        except Exception as e:
            print(f"❌ Enhanced face detection failed: {e}")
            return False, None, 0.0
    
    def detect(self, frame: np.ndarray) -> List[Detection]:
        """
        🚀 Main detection function - persons + proxy features
        
        Args:
            frame: Input frame (BGR format)
            
        Returns:
            List of detections with proxy features
        """
        start_time = time.time()
        self.frame_count += 1
        
        # Detect persons first
        person_detections = self.detect_persons(frame)
        
        # Add proxy features to each person
        enhanced_detections = []
        
        for detection in person_detections:
            # Detect proxy feature (face) within person bbox
            has_proxy, proxy_bbox, proxy_conf = self.detect_proxy_features(
                frame, detection.bbox
            )
            
            # Update detection with proxy information
            detection.has_proxy_feature = has_proxy
            detection.proxy_bbox = proxy_bbox
            detection.proxy_confidence = proxy_conf
            
            enhanced_detections.append(detection)
        
        # Track performance
        total_time = time.time() - start_time
        self.detection_times.append(total_time)
        
        # Keep only recent times for averaging
        if len(self.detection_times) > 100:
            self.detection_times = self.detection_times[-100:]
        
        return enhanced_detections
    
    def get_performance_stats(self) -> Dict[str, float]:
        """Get detector performance statistics"""
        if not self.detection_times:
            return {"avg_time": 0.0, "fps": 0.0}
        
        avg_time = np.mean(self.detection_times)
        fps = 1.0 / avg_time if avg_time > 0 else 0.0
        
        return {
            "avg_time": avg_time,
            "fps": fps,
            "frame_count": self.frame_count
        }
    
    def cleanup(self):
        """Cleanup resources"""
        if hasattr(self, 'person_model'):
            del self.person_model
        
        print("🧹 Detector cleanup completed")
