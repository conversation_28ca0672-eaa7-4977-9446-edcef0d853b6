#!/usr/bin/env python3
"""
🚀 AutoZoom Consolidated Demo - Main Application
===============================================

Life-saving AI-powered safety monitoring with confidence-based zoom.
Demonstrates automatic zoom behavior when safety feature confidence drops.

This consolidated demo brings together:
- Person detection with proxy features (face as helmet proxy)
- Energy-based tracking with ID preservation (E ∝ zoom²)
- Confidence monitoring with streak detection
- Automatic zoom triggering and PTZ simulation
- Comprehensive visualization and performance metrics

Built with love for worker protection and safety! 💙
"""

import cv2
import numpy as np
import argparse
import time
import sys
import os
from typing import Dict, Any, Optional

# Import all demo components
from config import config
from detector import PersonProxyDetector
from tracker import EnergyBasedTracker
from confidence_monitor import ConfidenceMonitor
from autozoom import AutoZoomController
from zoom_simulator import PTZZoomSimulator
from visualizer import AutoZoomVisualizer

class AutoZoomDemo:
    """
    🚀 AutoZoom Consolidated Demo System
    
    Integrates all components into a complete life-saving safety monitoring
    demonstration that shows confidence-based zoom triggering in action.
    
    System Flow:
    1. Detect persons and proxy features (face as helmet proxy)
    2. Track with energy-based Re-ID (preserves IDs during zoom)
    3. Monitor confidence per track (simulates occlusion drops)
    4. Trigger zoom when confidence < threshold
    5. Apply PTZ simulation via crop + resize
    6. Visualize all system states and performance
    """
    
    def __init__(self):
        """Initialize complete AutoZoom demo system"""
        
        print("🚀 Initializing AutoZoom Consolidated Demo System...")
        print("=" * 60)
        
        # Initialize all components
        self.detector = PersonProxyDetector()
        self.tracker = EnergyBasedTracker()
        self.confidence_monitor = ConfidenceMonitor()
        self.autozoom_controller = AutoZoomController()
        self.zoom_simulator = PTZZoomSimulator()
        self.visualizer = AutoZoomVisualizer()
        
        # Demo state
        self.frame_count = 0
        self.start_time = time.time()
        self.total_processing_time = 0.0
        
        # Performance tracking
        self.fps_history = []
        self.processing_times = []
        
        print("✅ AutoZoom Demo System initialized successfully!")
        print(f"🎯 Device: {config.get_device()}")
        print(f"🔍 Confidence threshold: {config.confidence_threshold}")
        print(f"📹 Zoom scale: {config.zoom_scale}x")
        print("=" * 60)
    
    def process_frame(self, frame: np.ndarray) -> Dict[str, Any]:
        """
        🔄 Process single frame through complete pipeline
        
        Args:
            frame: Input frame
            
        Returns:
            Processing results and performance metrics
        """
        frame_start_time = time.time()
        self.frame_count += 1
        
        # Step 1: Person and proxy feature detection
        detections = self.detector.detect(frame)
        
        # Step 2: Energy-based tracking with ID preservation
        current_zoom_level = self.autozoom_controller.current_zoom_level
        tracks = self.tracker.update(detections, frame, current_zoom_level)
        
        # Step 3: Confidence monitoring and streak detection
        confidence_records = self.confidence_monitor.update(tracks)
        
        # Step 4: AutoZoom decision making
        autozoom_result = self.autozoom_controller.update(tracks, confidence_records)
        
        # Step 5: Apply zoom simulation if needed
        if autozoom_result['is_zooming']:
            zoomed_frame = self.zoom_simulator.apply_zoom(
                frame,
                autozoom_result['zoom_level'],
                autozoom_result['zoom_center'],
                autozoom_result['zoom_bbox']
            )
        else:
            zoomed_frame = frame
            self.zoom_simulator.reset_zoom()

        # Step 6: Transform track coordinates for zoomed view
        zoom_info = self.zoom_simulator.get_zoom_info()
        transformed_tracks = []

        if zoom_info['is_zoomed']:
            # Only show tracks that are visible in the zoomed view
            zoom_target_id = autozoom_result['zoom_target'].track_id if autozoom_result['zoom_target'] else None

            for track in tracks:
                # Transform track coordinates
                transformed_bbox = self.zoom_simulator.transform_coordinates_to_zoomed_view(
                    track.bbox, zoom_info['crop_region']
                )

                if transformed_bbox is not None:
                    # Create a copy of the track with transformed coordinates
                    transformed_track = track
                    transformed_track.bbox = transformed_bbox

                    # Transform proxy bbox if present
                    if track.proxy_bbox:
                        transformed_proxy = self.zoom_simulator.transform_coordinates_to_zoomed_view(
                            track.proxy_bbox, zoom_info['crop_region']
                        )
                        transformed_track.proxy_bbox = transformed_proxy

                    # Update center coordinates
                    transformed_track.center = (
                        (transformed_bbox[0] + transformed_bbox[2]) / 2,
                        (transformed_bbox[1] + transformed_bbox[3]) / 2
                    )

                    transformed_tracks.append(transformed_track)
        else:
            transformed_tracks = tracks

        # Step 7: Visualization with transformed coordinates
        performance_stats = self.get_performance_stats()
        vis_frame = self.visualizer.render_frame(
            zoomed_frame, transformed_tracks, confidence_records, autozoom_result, performance_stats
        )
        
        # Track processing time
        processing_time = time.time() - frame_start_time
        self.processing_times.append(processing_time)
        self.total_processing_time += processing_time
        
        # Calculate FPS
        if processing_time > 0:
            current_fps = 1.0 / processing_time
            self.fps_history.append(current_fps)
        
        # Keep only recent history
        if len(self.fps_history) > 100:
            self.fps_history = self.fps_history[-100:]
        if len(self.processing_times) > 100:
            self.processing_times = self.processing_times[-100:]
        
        return {
            'processed_frame': vis_frame,
            'detections': detections,
            'tracks': tracks,
            'confidence_records': confidence_records,
            'autozoom_result': autozoom_result,
            'performance_stats': performance_stats,
            'processing_time': processing_time
        }
    
    def get_performance_stats(self) -> Dict[str, Dict[str, Any]]:
        """Get comprehensive performance statistics"""
        return {
            'detector': self.detector.get_performance_stats(),
            'tracker': self.tracker.get_performance_stats(),
            'monitor': self.confidence_monitor.get_performance_stats(),
            'autozoom': self.autozoom_controller.get_performance_stats(),
            'zoom_sim': self.zoom_simulator.get_performance_stats(),
            'confidence_summary': self.confidence_monitor.get_confidence_summary(),
            'overall': {
                'frame_count': self.frame_count,
                'avg_fps': np.mean(self.fps_history) if self.fps_history else 0.0,
                'avg_processing_time': np.mean(self.processing_times) if self.processing_times else 0.0,
                'total_time': time.time() - self.start_time
            }
        }
    
    def run_video_demo(self, video_path: str, output_path: Optional[str] = None, display: bool = True) -> bool:
        """
        🎬 Run demo on video file
        
        Args:
            video_path: Path to input video
            output_path: Optional output video path
            display: Whether to display video window
            
        Returns:
            True if successful
        """
        print(f"🎬 Starting video demo: {video_path}")
        
        # Open video
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            print(f"❌ Failed to open video: {video_path}")
            return False
        
        # Get video properties
        fps = int(cap.get(cv2.CAP_PROP_FPS))
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        
        print(f"📹 Video: {width}x{height} @ {fps} FPS, {total_frames} frames")
        
        # Setup video writer if output requested
        writer = None
        if output_path:
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            writer = cv2.VideoWriter(output_path, fourcc, config.target_fps, (width, height))
            print(f"💾 Output: {output_path}")
        
        # Create display window if needed
        if display:
            cv2.namedWindow('AutoZoom Demo', cv2.WINDOW_NORMAL)
            cv2.resizeWindow('AutoZoom Demo', config.display_width, config.display_height)
        
        print("🚀 Processing started... Press 'q' to quit")
        print("-" * 60)
        
        try:
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                # Process frame
                result = self.process_frame(frame)
                processed_frame = result['processed_frame']
                
                # Write to output if enabled
                if writer:
                    writer.write(processed_frame)
                
                # Display if enabled
                if display:
                    cv2.imshow('AutoZoom Demo', processed_frame)
                    
                    key = cv2.waitKey(1) & 0xFF
                    if key == ord('q'):
                        print("\n🛑 Demo stopped by user")
                        break
                
                # Print progress
                if self.frame_count % 30 == 0:  # Every 30 frames
                    stats = self.get_performance_stats()
                    overall_stats = stats['overall']
                    print(f"Frame {self.frame_count:4d}/{total_frames} | "
                          f"FPS: {overall_stats['avg_fps']:5.1f} | "
                          f"Tracks: {stats['tracker']['confirmed_tracks']:2d} | "
                          f"Zoom Ops: {stats['autozoom']['zoom_operations']:2d}")
        
        except KeyboardInterrupt:
            print("\n🛑 Demo interrupted by user")
        
        finally:
            # Cleanup
            cap.release()
            if writer:
                writer.release()
            if display:
                cv2.destroyAllWindows()
        
        # Final statistics
        print("\n" + "=" * 60)
        print("📊 FINAL PERFORMANCE STATISTICS")
        print("=" * 60)
        
        final_stats = self.get_performance_stats()
        overall = final_stats['overall']
        autozoom = final_stats['autozoom']
        confidence = final_stats['confidence_summary']
        
        print(f"🎬 Total Frames Processed: {overall['frame_count']}")
        print(f"⏱️  Total Processing Time: {overall['total_time']:.1f}s")
        print(f"🚀 Average FPS: {overall['avg_fps']:.1f}")
        print(f"🎯 Total Tracks: {final_stats['tracker']['total_tracks']}")
        print(f"🔍 Zoom Operations: {autozoom['zoom_operations']}")
        print(f"✅ Successful Zooms: {autozoom['successful_zooms']}")
        print(f"📈 Success Rate: {autozoom['success_rate']:.1%}")
        print(f"⚠️  Low Confidence Events: {confidence.get('low_confidence', 0)}")
        print(f"🚨 Critical Events: {confidence.get('critical_confidence', 0)}")
        
        print("\n💙 Demo completed successfully!")
        print("Built with love for worker protection and safety!")
        
        return True
    
    def cleanup(self):
        """Cleanup all components"""
        print("🧹 Cleaning up AutoZoom Demo System...")
        
        if hasattr(self, 'detector'):
            self.detector.cleanup()
        
        print("✅ Cleanup completed")

def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(
        description="AutoZoom Consolidated Demo - Life-Saving AI Safety Monitoring",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main.py --video videos/test_input.mp4
  python main.py --video videos/test_input.mp4 --output results/demo_output.mp4
  python main.py --video videos/test_input.mp4 --no-display
  
Built with love for worker protection and safety! 💙
        """
    )
    
    parser.add_argument('--video', type=str, default=config.default_video_path,
                       help='Input video path')
    parser.add_argument('--output', type=str, default=None,
                       help='Output video path (optional)')
    parser.add_argument('--no-display', action='store_true',
                       help='Disable video display window')
    
    args = parser.parse_args()
    
    # Validate input video
    if not os.path.exists(args.video):
        print(f"❌ Video file not found: {args.video}")
        print(f"💡 Please provide a valid video file or place test video at: {config.default_video_path}")
        return 1
    
    # Create output directory if needed
    if args.output:
        output_dir = os.path.dirname(args.output)
        if output_dir:
            os.makedirs(output_dir, exist_ok=True)
    
    # Initialize and run demo
    demo = None
    try:
        demo = AutoZoomDemo()
        success = demo.run_video_demo(
            video_path=args.video,
            output_path=args.output,
            display=not args.no_display
        )
        
        return 0 if success else 1
        
    except Exception as e:
        print(f"❌ Demo failed with error: {e}")
        import traceback
        traceback.print_exc()
        return 1
        
    finally:
        if demo:
            demo.cleanup()

if __name__ == "__main__":
    sys.exit(main())
