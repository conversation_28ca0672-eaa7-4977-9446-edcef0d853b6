"""
🎨 AutoZoom Visualization System
===============================

Draws bounding boxes, IDs, confidence levels, zoom state, and debug information
for the AutoZoom demo system. Provides clear visual feedback for all system
components and their states.

Visualization Features:
- Person bounding boxes with track IDs
- Proxy feature indicators (face detection)
- Confidence meters and state indicators
- Zoom state and target visualization
- Performance metrics overlay
- System status indicators

Built with love for worker protection and safety! 💙
"""

import cv2
import numpy as np
import time
from typing import List, Dict, Tuple, Optional, Any

from config import config
from tracker import Track
from confidence_monitor import ConfidenceRecord, ConfidenceState
from autozoom import ZoomState, ZoomTarget

class AutoZoomVisualizer:
    """
    🎨 AutoZoom Visualization System
    
    Provides comprehensive visual feedback for all AutoZoom system components
    including detection, tracking, confidence monitoring, and zoom operations.
    
    Key Features:
    1. Multi-colored bounding boxes based on confidence state
    2. Track ID and confidence display
    3. Zoom state visualization
    4. Performance metrics overlay
    5. System status indicators
    6. Debug information display
    """
    
    def __init__(self):
        """Initialize visualizer"""
        
        print("🎨 Initializing AutoZoom Visualizer...")
        
        # Font settings
        self.font = cv2.FONT_HERSHEY_SIMPLEX
        self.font_scale = config.font_scale
        self.font_thickness = config.font_thickness
        
        # Color scheme
        self.colors = {
            'person_box': config.color_person_box,
            'face_box': config.color_face_box,
            'low_confidence': config.color_low_confidence,
            'zoom_indicator': config.color_zoom_indicator,
            'text': config.color_text,
            'high_confidence': (0, 255, 0),      # Green
            'medium_confidence': (0, 255, 255),  # Yellow
            'critical_confidence': (0, 0, 255),  # Red
            'background': (0, 0, 0),             # Black
            'success': (0, 255, 0),              # Green
            'warning': (0, 255, 255),            # Yellow
            'error': (0, 0, 255)                 # Red
        }
        
        # Layout settings
        self.info_panel_width = 300
        self.info_panel_height = 200
        self.margin = 10
        
        print("✅ AutoZoom Visualizer initialized")
    
    def get_confidence_color(self, confidence_state: ConfidenceState) -> Tuple[int, int, int]:
        """Get color based on confidence state"""
        color_map = {
            ConfidenceState.HIGH: self.colors['high_confidence'],
            ConfidenceState.MEDIUM: self.colors['medium_confidence'],
            ConfidenceState.LOW: self.colors['warning'],
            ConfidenceState.CRITICAL: self.colors['critical_confidence']
        }
        return color_map.get(confidence_state, self.colors['text'])
    
    def draw_bounding_box(self, frame: np.ndarray, bbox: Tuple[int, int, int, int], color: Tuple[int, int, int], thickness: int = 2):
        """Draw bounding box on frame"""
        x1, y1, x2, y2 = bbox
        cv2.rectangle(frame, (x1, y1), (x2, y2), color, thickness)
    
    def draw_text_with_background(self, frame: np.ndarray, text: str, position: Tuple[int, int], color: Tuple[int, int, int], bg_color: Optional[Tuple[int, int, int]] = None):
        """Draw text with optional background"""
        x, y = position
        
        # Get text size
        (text_width, text_height), baseline = cv2.getTextSize(text, self.font, self.font_scale, self.font_thickness)
        
        # Draw background if specified
        if bg_color:
            cv2.rectangle(frame, 
                         (x - 2, y - text_height - 2), 
                         (x + text_width + 2, y + baseline + 2), 
                         bg_color, -1)
        
        # Draw text
        cv2.putText(frame, text, (x, y), self.font, self.font_scale, color, self.font_thickness)
    
    def draw_confidence_meter(self, frame: np.ndarray, position: Tuple[int, int], confidence: float, state: ConfidenceState):
        """Draw confidence meter visualization"""
        x, y = position
        meter_width = 60
        meter_height = 8
        
        # Draw meter background
        cv2.rectangle(frame, (x, y), (x + meter_width, y + meter_height), (50, 50, 50), -1)
        
        # Draw confidence fill
        fill_width = int(meter_width * confidence)
        color = self.get_confidence_color(state)
        cv2.rectangle(frame, (x, y), (x + fill_width, y + meter_height), color, -1)
        
        # Draw meter border
        cv2.rectangle(frame, (x, y), (x + meter_width, y + meter_height), self.colors['text'], 1)
        
        # Draw confidence text
        conf_text = f"{confidence:.2f}"
        self.draw_text_with_background(frame, conf_text, (x + meter_width + 5, y + meter_height), color)
    
    def draw_track_info(self, frame: np.ndarray, track: Track, confidence_record: Optional[ConfidenceRecord], is_zoom_target: bool = False):
        """Draw track information overlay"""
        x1, y1, x2, y2 = track.bbox

        # Determine box color based on confidence state
        if confidence_record:
            box_color = self.get_confidence_color(confidence_record.confidence_state)
        else:
            box_color = self.colors['person_box']

        # Highlight zoom target with thicker border
        box_thickness = 4 if is_zoom_target else 2

        # Draw person bounding box
        self.draw_bounding_box(frame, track.bbox, box_color, box_thickness)

        # Draw zoom target indicator
        if is_zoom_target:
            # Draw corner markers for zoom target
            corner_size = 20
            corner_thickness = 3

            # Top-left corner
            cv2.line(frame, (x1, y1), (x1 + corner_size, y1), self.colors['zoom_indicator'], corner_thickness)
            cv2.line(frame, (x1, y1), (x1, y1 + corner_size), self.colors['zoom_indicator'], corner_thickness)

            # Top-right corner
            cv2.line(frame, (x2, y1), (x2 - corner_size, y1), self.colors['zoom_indicator'], corner_thickness)
            cv2.line(frame, (x2, y1), (x2, y1 + corner_size), self.colors['zoom_indicator'], corner_thickness)

            # Bottom-left corner
            cv2.line(frame, (x1, y2), (x1 + corner_size, y2), self.colors['zoom_indicator'], corner_thickness)
            cv2.line(frame, (x1, y2), (x1, y2 - corner_size), self.colors['zoom_indicator'], corner_thickness)

            # Bottom-right corner
            cv2.line(frame, (x2, y2), (x2 - corner_size, y2), self.colors['zoom_indicator'], corner_thickness)
            cv2.line(frame, (x2, y2), (x2, y2 - corner_size), self.colors['zoom_indicator'], corner_thickness)

        # Draw proxy feature (face) if present
        if track.has_proxy_feature and track.proxy_bbox:
            self.draw_bounding_box(frame, track.proxy_bbox, self.colors['face_box'], 1)

        # Draw track ID
        id_text = f"ID: {track.track_id}"
        if is_zoom_target:
            id_text += " [ZOOM TARGET]"
        self.draw_text_with_background(frame, id_text, (x1, y1 - 25), self.colors['text'], self.colors['background'])

        # Draw confidence information
        if confidence_record:
            # Confidence meter
            self.draw_confidence_meter(frame, (x1, y1 - 15), confidence_record.smoothed_confidence, confidence_record.confidence_state)

            # Confidence state text
            state_text = confidence_record.confidence_state.value.upper()
            state_color = self.get_confidence_color(confidence_record.confidence_state)
            self.draw_text_with_background(frame, state_text, (x1, y2 + 15), state_color, self.colors['background'])

            # Streak information for low confidence
            if confidence_record.low_confidence_streak > 0:
                streak_text = f"Streak: {confidence_record.low_confidence_streak}"
                self.draw_text_with_background(frame, streak_text, (x1, y2 + 35), self.colors['warning'], self.colors['background'])

            # Zoom recommendation indicator
            if confidence_record.zoom_recommended:
                zoom_text = "ZOOM RECOMMENDED"
                self.draw_text_with_background(frame, zoom_text, (x1, y2 + 55), self.colors['zoom_indicator'], self.colors['background'])
    
    def draw_zoom_indicator(self, frame: np.ndarray, zoom_state: ZoomState, zoom_level: float, zoom_target: Optional[ZoomTarget]):
        """Draw zoom state indicator"""
        height, width = frame.shape[:2]
        
        # Zoom state indicator
        state_text = f"ZOOM: {zoom_state.value.upper()}"
        state_color = self.colors['zoom_indicator'] if zoom_state != ZoomState.NORMAL else self.colors['text']
        self.draw_text_with_background(frame, state_text, (width - 200, 30), state_color, self.colors['background'])
        
        # Zoom level indicator
        level_text = f"Level: {zoom_level:.1f}x"
        self.draw_text_with_background(frame, level_text, (width - 200, 50), self.colors['text'], self.colors['background'])
        
        # Zoom target indicator
        if zoom_target:
            target_text = f"Target: ID {zoom_target.track_id}"
            self.draw_text_with_background(frame, target_text, (width - 200, 70), self.colors['zoom_indicator'], self.colors['background'])
    
    def draw_performance_metrics(self, frame: np.ndarray, detector_stats: Dict, tracker_stats: Dict, monitor_stats: Dict, autozoom_stats: Dict, zoom_sim_stats: Dict):
        """Draw performance metrics overlay"""
        height, width = frame.shape[:2]
        
        # Performance panel background
        panel_x = 10
        panel_y = height - 150
        panel_width = 280
        panel_height = 140
        
        # Semi-transparent background
        overlay = frame.copy()
        cv2.rectangle(overlay, (panel_x, panel_y), (panel_x + panel_width, panel_y + panel_height), self.colors['background'], -1)
        cv2.addWeighted(overlay, 0.7, frame, 0.3, 0, frame)
        
        # Performance metrics
        y_offset = panel_y + 20
        line_height = 18
        
        metrics = [
            f"Detector: {detector_stats.get('fps', 0):.1f} FPS",
            f"Tracker: {tracker_stats.get('fps', 0):.1f} FPS", 
            f"Monitor: {monitor_stats.get('fps', 0):.1f} FPS",
            f"AutoZoom: {autozoom_stats.get('fps', 0):.1f} FPS",
            f"Zoom Sim: {zoom_sim_stats.get('fps', 0):.1f} FPS",
            f"Tracks: {tracker_stats.get('confirmed_tracks', 0)}",
            f"Zoom Ops: {autozoom_stats.get('zoom_operations', 0)}",
            f"Success Rate: {autozoom_stats.get('success_rate', 0):.1%}"
        ]
        
        for i, metric in enumerate(metrics):
            self.draw_text_with_background(frame, metric, (panel_x + 10, y_offset + i * line_height), self.colors['text'])
    
    def draw_system_status(self, frame: np.ndarray, confidence_summary: Dict):
        """Draw system status indicators"""
        height, width = frame.shape[:2]
        
        # Status panel
        status_x = width - 250
        status_y = 100
        
        # Confidence distribution
        total_tracks = confidence_summary.get('total_tracks', 0)
        if total_tracks > 0:
            high_count = confidence_summary.get('high_confidence', 0)
            medium_count = confidence_summary.get('medium_confidence', 0)
            low_count = confidence_summary.get('low_confidence', 0)
            critical_count = confidence_summary.get('critical_confidence', 0)
            zoom_count = confidence_summary.get('zoom_recommended', 0)
            
            status_lines = [
                f"Total Tracks: {total_tracks}",
                f"High Conf: {high_count}",
                f"Medium Conf: {medium_count}",
                f"Low Conf: {low_count}",
                f"Critical: {critical_count}",
                f"Zoom Needed: {zoom_count}"
            ]
            
            for i, line in enumerate(status_lines):
                color = self.colors['text']
                if 'Critical' in line and critical_count > 0:
                    color = self.colors['critical_confidence']
                elif 'Zoom Needed' in line and zoom_count > 0:
                    color = self.colors['zoom_indicator']
                
                self.draw_text_with_background(frame, line, (status_x, status_y + i * 20), color, self.colors['background'])
    
    def render_frame(self, frame: np.ndarray, tracks: List[Track], confidence_records: Dict[int, ConfidenceRecord], autozoom_result: Dict[str, Any], performance_stats: Dict[str, Dict]) -> np.ndarray:
        """
        🚀 Main frame rendering function
        
        Args:
            frame: Input frame
            tracks: List of tracks to visualize
            confidence_records: Confidence records for tracks
            autozoom_result: AutoZoom system result
            performance_stats: Performance statistics from all components
            
        Returns:
            Rendered frame with all visualizations
        """
        # Create copy for rendering
        vis_frame = frame.copy()
        
        # Draw track information
        zoom_target_id = autozoom_result.get('zoom_target').track_id if autozoom_result.get('zoom_target') else None

        for track in tracks:
            confidence_record = confidence_records.get(track.track_id)
            is_zoom_target = (track.track_id == zoom_target_id)
            self.draw_track_info(vis_frame, track, confidence_record, is_zoom_target)
        
        # Draw zoom indicators
        zoom_state = autozoom_result.get('zoom_state', ZoomState.NORMAL)
        zoom_level = autozoom_result.get('zoom_level', 1.0)
        zoom_target = autozoom_result.get('zoom_target')
        
        self.draw_zoom_indicator(vis_frame, zoom_state, zoom_level, zoom_target)
        
        # Draw performance metrics if enabled
        if config.show_debug_info:
            self.draw_performance_metrics(
                vis_frame,
                performance_stats.get('detector', {}),
                performance_stats.get('tracker', {}),
                performance_stats.get('monitor', {}),
                performance_stats.get('autozoom', {}),
                performance_stats.get('zoom_sim', {})
            )
        
        # Draw system status
        confidence_summary = performance_stats.get('confidence_summary', {})
        self.draw_system_status(vis_frame, confidence_summary)
        
        # Draw title
        title = "AutoZoom Demo - Life-Saving AI Safety Monitoring 💙"
        self.draw_text_with_background(vis_frame, title, (10, 25), self.colors['text'], self.colors['background'])
        
        return vis_frame
    
    def create_info_overlay(self, frame_shape: Tuple[int, int]) -> np.ndarray:
        """Create information overlay for demo"""
        height, width = frame_shape
        overlay = np.zeros((height, width, 3), dtype=np.uint8)
        
        # Instructions
        instructions = [
            "AutoZoom Demo Controls:",
            "- System automatically zooms on low confidence",
            "- Green boxes: High confidence",
            "- Yellow boxes: Medium confidence", 
            "- Red boxes: Low/Critical confidence",
            "- Blue boxes: Face detection (helmet proxy)",
            "- Crosshair: Current zoom target",
            "",
            "Built with love for worker safety! 💙"
        ]
        
        y_start = 50
        for i, instruction in enumerate(instructions):
            color = self.colors['zoom_indicator'] if i == 0 else self.colors['text']
            self.draw_text_with_background(overlay, instruction, (20, y_start + i * 25), color)
        
        return overlay
