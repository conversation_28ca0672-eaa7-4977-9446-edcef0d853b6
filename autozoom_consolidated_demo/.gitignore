# AutoZoom Consolidated Demo - Git Ignore
# Built with love for worker protection and safety! 💙

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environment
.venv/
venv/
ENV/
env/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Model files (downloaded automatically)
*.pt
*.pth
*.onnx
models/*.pt
models/*.pth
models/*.onnx

# Output videos (keep structure, ignore content)
results/*.mp4
results/*.avi
results/*.mov

# Logs
*.log
logs/

# Temporary files
*.tmp
*.temp
temp/

# Test videos (too large for git)
videos/*.mp4
videos/*.avi
videos/*.mov

# Keep directory structure
!videos/.gitkeep
!results/.gitkeep
!models/.gitkeep
