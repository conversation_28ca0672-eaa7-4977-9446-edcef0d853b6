"""
📹 PTZ Zoom Simulator
====================

Simulates PTZ (Pan-Tilt-Zoom) camera behavior through intelligent crop + resize
operations. Provides smooth zoom transitions and realistic camera movement.

Core Features:
- Smooth zoom transitions with configurable duration
- Intelligent crop region calculation with padding
- Aspect ratio preservation
- Motion prediction for moving targets
- Realistic PTZ camera movement simulation

Built with love for worker protection and safety! 💙
"""

import cv2
import numpy as np
import time
from typing import Tuple, Optional, Dict, Any
from dataclasses import dataclass

from config import config

@dataclass
class ZoomRegion:
    """
    📹 Zoom region specification
    
    Defines the crop region and zoom parameters for PTZ simulation
    """
    # Crop region in original frame coordinates
    x1: int
    y1: int
    x2: int
    y2: int
    
    # Zoom parameters
    zoom_level: float
    center_x: float
    center_y: float
    
    # Transition parameters
    transition_progress: float = 0.0
    smooth_factor: float = 0.8

class PTZZoomSimulator:
    """
    📹 PTZ Zoom Simulator
    
    Simulates realistic PTZ camera behavior through intelligent frame processing.
    Applies zoom via crop + resize operations while maintaining smooth transitions
    and realistic camera movement patterns.
    
    Key Features:
    1. Smooth zoom transitions
    2. Intelligent crop region calculation
    3. Motion prediction and tracking
    4. Aspect ratio preservation
    5. Realistic PTZ movement simulation
    """
    
    def __init__(self):
        """Initialize PTZ zoom simulator"""
        
        print("📹 Initializing PTZ Zoom Simulator...")
        
        # Current zoom state
        self.current_zoom_level = 1.0
        self.current_center = (0.5, 0.5)  # Normalized coordinates (0-1)
        self.target_center = (0.5, 0.5)
        
        # Smooth transition state
        self.transition_start_time = None
        self.transition_duration = 0.5  # seconds
        self.smoothing_factor = config.zoom_center_smoothing
        
        # Frame dimensions
        self.frame_width = 0
        self.frame_height = 0
        
        # Performance tracking
        self.processing_times = []
        self.frame_count = 0
        
        print("✅ PTZ Zoom Simulator initialized")
    
    def update_frame_dimensions(self, frame: np.ndarray):
        """Update internal frame dimensions"""
        self.frame_height, self.frame_width = frame.shape[:2]
    
    def calculate_crop_region(self, zoom_level: float, center: Tuple[float, float], frame_shape: Tuple[int, int]) -> ZoomRegion:
        """
        📐 Calculate optimal crop region for zoom level and center
        
        Args:
            zoom_level: Zoom magnification (1.0 = no zoom)
            center: Zoom center in normalized coordinates (0-1)
            frame_shape: (height, width) of original frame
            
        Returns:
            ZoomRegion with crop coordinates
        """
        height, width = frame_shape
        center_x, center_y = center
        
        # Calculate crop dimensions
        crop_width = int(width / zoom_level)
        crop_height = int(height / zoom_level)
        
        # Ensure minimum crop size
        crop_width = max(crop_width, config.min_crop_size[0])
        crop_height = max(crop_height, config.min_crop_size[1])
        
        # Calculate crop center in pixel coordinates
        center_px_x = int(center_x * width)
        center_px_y = int(center_y * height)
        
        # Calculate crop bounds
        x1 = center_px_x - crop_width // 2
        y1 = center_px_y - crop_height // 2
        x2 = x1 + crop_width
        y2 = y1 + crop_height
        
        # Ensure crop region is within frame bounds
        if x1 < 0:
            x2 -= x1
            x1 = 0
        if y1 < 0:
            y2 -= y1
            y1 = 0
        if x2 > width:
            x1 -= (x2 - width)
            x2 = width
        if y2 > height:
            y1 -= (y2 - height)
            y2 = height
        
        # Final bounds check
        x1 = max(0, x1)
        y1 = max(0, y1)
        x2 = min(width, x2)
        y2 = min(height, y2)
        
        return ZoomRegion(
            x1=x1, y1=y1, x2=x2, y2=y2,
            zoom_level=zoom_level,
            center_x=center_x,
            center_y=center_y
        )
    
    def bbox_to_normalized_center(self, bbox: Tuple[int, int, int, int], frame_shape: Tuple[int, int]) -> Tuple[float, float]:
        """
        🎯 Convert bounding box to normalized center coordinates
        
        Args:
            bbox: Bounding box (x1, y1, x2, y2)
            frame_shape: (height, width) of frame
            
        Returns:
            Normalized center coordinates (0-1)
        """
        height, width = frame_shape
        x1, y1, x2, y2 = bbox
        
        center_x = (x1 + x2) / 2 / width
        center_y = (y1 + y2) / 2 / height
        
        # Clamp to valid range
        center_x = max(0.0, min(1.0, center_x))
        center_y = max(0.0, min(1.0, center_y))
        
        return center_x, center_y
    
    def smooth_center_transition(self, target_center: Tuple[float, float], dt: float) -> Tuple[float, float]:
        """
        🌊 Apply smooth transition to zoom center
        
        Args:
            target_center: Target center coordinates
            dt: Time delta for smooth transition
            
        Returns:
            Smoothed center coordinates
        """
        current_x, current_y = self.current_center
        target_x, target_y = target_center
        
        # Apply exponential smoothing
        alpha = 1.0 - np.exp(-dt / self.smoothing_factor)
        
        new_x = current_x + alpha * (target_x - current_x)
        new_y = current_y + alpha * (target_y - current_y)
        
        return new_x, new_y
    
    def apply_zoom(self, frame: np.ndarray, zoom_level: float, zoom_center: Optional[Tuple[float, float]] = None, zoom_bbox: Optional[Tuple[int, int, int, int]] = None) -> np.ndarray:
        """
        🔍 Apply zoom to frame via crop + resize
        
        Args:
            frame: Input frame
            zoom_level: Zoom magnification (1.0 = no zoom)
            zoom_center: Zoom center in normalized coordinates (0-1)
            zoom_bbox: Alternative - zoom to this bounding box
            
        Returns:
            Zoomed frame
        """
        start_time = time.time()
        self.frame_count += 1
        
        # Update frame dimensions
        self.update_frame_dimensions(frame)
        
        # Handle no zoom case
        if zoom_level <= 1.0:
            self.current_zoom_level = 1.0
            self.current_center = (0.5, 0.5)
            return frame
        
        # Determine target center
        if zoom_bbox is not None:
            target_center = self.bbox_to_normalized_center(zoom_bbox, frame.shape[:2])
        elif zoom_center is not None:
            target_center = zoom_center
        else:
            target_center = (0.5, 0.5)  # Center of frame
        
        # Apply smooth center transition
        dt = time.time() - (self.transition_start_time or time.time())
        if self.transition_start_time is None:
            self.transition_start_time = time.time()
        
        smoothed_center = self.smooth_center_transition(target_center, dt)
        self.current_center = smoothed_center
        self.current_zoom_level = zoom_level
        
        # Calculate crop region
        crop_region = self.calculate_crop_region(zoom_level, smoothed_center, frame.shape[:2])
        
        # Extract crop region
        cropped_frame = frame[crop_region.y1:crop_region.y2, crop_region.x1:crop_region.x2]
        
        if cropped_frame.size == 0:
            return frame  # Return original if crop failed
        
        # Resize to original frame size
        if config.maintain_aspect_ratio:
            # Maintain aspect ratio with padding if needed
            zoomed_frame = self._resize_with_aspect_ratio(cropped_frame, (self.frame_width, self.frame_height))
        else:
            # Simple resize to target dimensions
            zoomed_frame = cv2.resize(cropped_frame, (self.frame_width, self.frame_height))
        
        # Track performance
        processing_time = time.time() - start_time
        self.processing_times.append(processing_time)
        
        if len(self.processing_times) > 100:
            self.processing_times = self.processing_times[-100:]
        
        return zoomed_frame
    
    def _resize_with_aspect_ratio(self, image: np.ndarray, target_size: Tuple[int, int]) -> np.ndarray:
        """
        📐 Resize image while maintaining aspect ratio
        
        Args:
            image: Input image
            target_size: (width, height) target size
            
        Returns:
            Resized image with maintained aspect ratio
        """
        target_width, target_height = target_size
        height, width = image.shape[:2]
        
        # Calculate scaling factor
        scale_x = target_width / width
        scale_y = target_height / height
        scale = min(scale_x, scale_y)
        
        # Calculate new dimensions
        new_width = int(width * scale)
        new_height = int(height * scale)
        
        # Resize image
        resized = cv2.resize(image, (new_width, new_height))
        
        # Create output image with target size
        result = np.zeros((target_height, target_width, image.shape[2]), dtype=image.dtype)
        
        # Center the resized image
        y_offset = (target_height - new_height) // 2
        x_offset = (target_width - new_width) // 2
        
        result[y_offset:y_offset + new_height, x_offset:x_offset + new_width] = resized
        
        return result
    
    def reset_zoom(self):
        """🔄 Reset zoom to normal view"""
        self.current_zoom_level = 1.0
        self.current_center = (0.5, 0.5)
        self.target_center = (0.5, 0.5)
        self.transition_start_time = None
    
    def transform_coordinates_to_zoomed_view(self, bbox: Tuple[int, int, int, int], crop_region: Optional[ZoomRegion] = None) -> Optional[Tuple[int, int, int, int]]:
        """
        🔄 Transform original frame coordinates to zoomed view coordinates

        Args:
            bbox: Bounding box in original frame coordinates (x1, y1, x2, y2)
            crop_region: Current crop region (if None, uses last applied zoom)

        Returns:
            Transformed bounding box in zoomed view coordinates, or None if outside view
        """
        if self.current_zoom_level <= 1.0:
            return bbox  # No transformation needed

        if crop_region is None:
            # Calculate current crop region
            crop_region = self.calculate_crop_region(
                self.current_zoom_level,
                self.current_center,
                (self.frame_height, self.frame_width)
            )

        x1, y1, x2, y2 = bbox

        # Check if bbox intersects with crop region
        if (x2 < crop_region.x1 or x1 > crop_region.x2 or
            y2 < crop_region.y1 or y1 > crop_region.y2):
            return None  # Bbox is outside the zoomed view

        # Transform coordinates relative to crop region
        crop_width = crop_region.x2 - crop_region.x1
        crop_height = crop_region.y2 - crop_region.y1

        # Translate to crop-relative coordinates
        rel_x1 = max(0, x1 - crop_region.x1)
        rel_y1 = max(0, y1 - crop_region.y1)
        rel_x2 = min(crop_width, x2 - crop_region.x1)
        rel_y2 = min(crop_height, y2 - crop_region.y1)

        # Scale to full frame size
        scale_x = self.frame_width / crop_width
        scale_y = self.frame_height / crop_height

        transformed_x1 = int(rel_x1 * scale_x)
        transformed_y1 = int(rel_y1 * scale_y)
        transformed_x2 = int(rel_x2 * scale_x)
        transformed_y2 = int(rel_y2 * scale_y)

        return (transformed_x1, transformed_y1, transformed_x2, transformed_y2)

    def get_zoom_info(self) -> Dict[str, Any]:
        """Get current zoom information"""
        crop_region = None
        if self.current_zoom_level > 1.0:
            crop_region = self.calculate_crop_region(
                self.current_zoom_level,
                self.current_center,
                (self.frame_height, self.frame_width)
            )

        return {
            "zoom_level": self.current_zoom_level,
            "center": self.current_center,
            "is_zoomed": self.current_zoom_level > 1.0,
            "frame_width": self.frame_width,
            "frame_height": self.frame_height,
            "crop_region": crop_region
        }
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get zoom simulator performance statistics"""
        if not self.processing_times:
            return {
                "avg_time": 0.0,
                "fps": 0.0,
                "frame_count": 0
            }
        
        avg_time = np.mean(self.processing_times)
        fps = 1.0 / avg_time if avg_time > 0 else 0.0
        
        return {
            "avg_time": avg_time,
            "fps": fps,
            "frame_count": self.frame_count,
            "current_zoom_level": self.current_zoom_level
        }
