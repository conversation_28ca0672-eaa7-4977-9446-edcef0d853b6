"""
🎯 Energy-Based Re-ID Tracker
============================

Implements Laplacian energy-based tracking that preserves IDs during zoom operations.
Core innovation: E ∝ zoom² scaling for robust tracking across zoom levels.

Mathematical Foundation:
- Laplacian energy: E = var(∇²I) where ∇² is the Laplacian operator
- Energy scaling: E_zoom = E_base × (zoom_level)²
- Track association via energy similarity and spatial proximity

Built with love for worker protection and safety! 💙
"""

import cv2
import numpy as np
import time
from typing import List, Dict, Tuple, Optional, Any
from dataclasses import dataclass, field
from collections import defaultdict, deque
from scipy.optimize import linear_sum_assignment
from scipy.spatial.distance import cdist

from config import config
from detector import Detection

@dataclass
class Track:
    """
    🎯 Single track representation with energy-based features
    
    Maintains track state including Laplacian energy for robust Re-ID
    """
    # Core identification
    track_id: int
    
    # Spatial information
    bbox: Tuple[int, int, int, int]
    center: Tuple[float, float]
    
    # Energy features (your innovation!)
    laplacian_energy: float
    energy_history: deque = field(default_factory=lambda: deque(maxlen=10))

    # Face-specific energy tracking
    face_energy: float = 0.0
    face_energy_history: deque = field(default_factory=lambda: deque(maxlen=10))
    
    # Proxy feature information
    has_proxy_feature: bool = False
    proxy_bbox: Optional[Tuple[int, int, int, int]] = None
    proxy_confidence: float = 0.0
    proxy_confidence_history: deque = field(default_factory=lambda: deque(maxlen=10))
    
    # Tracking state
    age: int = 0
    hits: int = 0
    time_since_update: int = 0
    confirmed: bool = False
    
    # Motion prediction
    velocity: Tuple[float, float] = (0.0, 0.0)
    predicted_center: Tuple[float, float] = (0.0, 0.0)
    
    # Zoom-related state
    zoom_level: float = 1.0
    zoom_adjusted_energy: float = 0.0
    
    # Timestamps
    first_seen: float = field(default_factory=time.time)
    last_seen: float = field(default_factory=time.time)

class EnergyBasedTracker:
    """
    🎯 Advanced Energy-Based Re-ID Tracker with Chain-of-Zoom Principles

    Implements your Laplacian energy innovation (E ∝ zoom²) with advanced
    ID preservation mechanisms from your Chain-of-Zoom work.

    Key Features:
    1. Multi-scale energy calculation with zoom decomposition
    2. AR-2 modeling for zoom state history
    3. Enhanced Hungarian algorithm with energy scaling validation
    4. Proxy feature confidence tracking
    5. Motion prediction with zoom-aware corrections
    6. Chain-of-Zoom decomposition for extreme zoom changes
    """

    def __init__(self):
        """Initialize advanced energy-based tracker"""

        print("🎯 Initializing Advanced Energy-Based Tracker...")

        # Track management
        self.tracks: Dict[int, Track] = {}
        self.next_track_id = 1

        # Laplacian kernel for energy calculation
        self.laplacian_kernel = np.array([
            [0, -1, 0],
            [-1, 4, -1],
            [0, -1, 0]
        ], dtype=np.float32)

        # Chain-of-Zoom state management
        self.current_zoom_level = 1.0
        self.zoom_state_history = deque(maxlen=10)  # AR-2 modeling
        self.extreme_zoom_threshold = 1.5  # Threshold for chain decomposition

        # Enhanced association parameters (face-focused)
        self.face_weight = 0.35        # Primary: Face-based association
        self.energy_weight = 0.25      # Secondary: Energy consistency
        self.spatial_weight = 0.25     # Secondary: Spatial proximity
        self.motion_weight = 0.15      # Tertiary: Motion prediction

        # Performance tracking
        self.frame_count = 0
        self.tracking_times = []
        self.id_switches = 0
        self.zoom_decompositions = 0

        print("✅ Advanced Energy-Based Tracker initialized")
    
    def calculate_laplacian_energy(self, frame: np.ndarray, bbox: Tuple[int, int, int, int], zoom_level: float = 1.0) -> float:
        """
        🔬 Calculate Laplacian energy with advanced zoom scaling validation

        Core Innovation: E ∝ zoom² scaling with Chain-of-Zoom principles

        Args:
            frame: Input frame (BGR)
            bbox: Bounding box (x1, y1, x2, y2)
            zoom_level: Current zoom level for energy scaling

        Returns:
            Scaled Laplacian energy value
        """
        try:
            x1, y1, x2, y2 = bbox

            # Extract ROI
            roi = frame[y1:y2, x1:x2]

            if roi.size == 0:
                return 0.0

            # Convert to grayscale
            gray_roi = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY)

            # Apply Laplacian filter
            laplacian = cv2.filter2D(gray_roi, cv2.CV_64F, self.laplacian_kernel)

            # Calculate energy as variance of Laplacian
            base_energy = np.var(laplacian)

            # Apply zoom scaling: E ∝ zoom² with validation
            if config.enable_energy_scaling:
                scaled_energy = base_energy * (zoom_level ** 2)

                # Validate energy scaling consistency (from your Chain-of-Zoom work)
                if len(self.zoom_state_history) > 0:
                    prev_zoom = self.zoom_state_history[-1].get('zoom_level', 1.0)
                    if prev_zoom > 0:
                        expected_ratio = (zoom_level / prev_zoom) ** 2
                        # This helps maintain energy consistency across zoom changes
                        scaling_factor = min(2.0, max(0.5, expected_ratio))  # Clamp for stability
                        scaled_energy = base_energy * scaling_factor * (zoom_level ** 2) / (expected_ratio + 1e-6)
            else:
                scaled_energy = base_energy

            return float(scaled_energy)

        except Exception as e:
            print(f"❌ Energy calculation failed: {e}")
            return 0.0

    def calculate_face_energy(self, frame: np.ndarray, face_bbox: Tuple[int, int, int, int], zoom_level: float = 1.0) -> float:
        """
        👤 Calculate Laplacian energy specifically for face region

        Face regions provide more stable energy signatures for ID preservation

        Args:
            frame: Input frame (BGR)
            face_bbox: Face bounding box (x1, y1, x2, y2)
            zoom_level: Current zoom level

        Returns:
            Face-specific energy value
        """
        try:
            x1, y1, x2, y2 = face_bbox

            # Extract face ROI
            face_roi = frame[y1:y2, x1:x2]

            if face_roi.size == 0:
                return 0.0

            # Convert to grayscale
            gray_face = cv2.cvtColor(face_roi, cv2.COLOR_BGR2GRAY)

            # Apply Gaussian blur to reduce noise in face region
            blurred_face = cv2.GaussianBlur(gray_face, (3, 3), 0.5)

            # Apply Laplacian filter
            laplacian = cv2.filter2D(blurred_face, cv2.CV_64F, self.laplacian_kernel)

            # Calculate energy as variance of Laplacian
            face_energy = np.var(laplacian)

            # Face energy scaling (more stable than full body)
            if config.enable_energy_scaling:
                scaled_energy = face_energy * (zoom_level ** 1.8)  # Slightly less aggressive scaling
            else:
                scaled_energy = face_energy

            return float(scaled_energy)

        except Exception as e:
            print(f"❌ Face energy calculation failed: {e}")
            return 0.0
    
    def predict_tracks(self):
        """🔮 Predict track positions based on motion"""
        for track in self.tracks.values():
            # Simple linear motion prediction
            cx, cy = track.center
            vx, vy = track.velocity
            
            track.predicted_center = (cx + vx, cy + vy)
            track.time_since_update += 1
    
    def calculate_association_cost(self, track: Track, detection: Detection, frame: np.ndarray, zoom_level: float) -> float:
        """
        💰 Enhanced association cost with Chain-of-Zoom energy validation

        Combines spatial distance, energy similarity, motion prediction, and proxy features
        with advanced zoom scaling validation from your Chain-of-Zoom work.

        Args:
            track: Existing track
            detection: New detection
            frame: Current frame
            zoom_level: Current zoom level

        Returns:
            Association cost (lower is better)
        """
        # 1. Spatial distance cost with motion prediction
        track_center = track.predicted_center
        det_center = (
            (detection.bbox[0] + detection.bbox[2]) / 2,
            (detection.bbox[1] + detection.bbox[3]) / 2
        )

        spatial_distance = np.sqrt(
            (track_center[0] - det_center[0]) ** 2 +
            (track_center[1] - det_center[1]) ** 2
        )

        # Normalize by frame size
        frame_diagonal = np.sqrt(frame.shape[0]**2 + frame.shape[1]**2)
        spatial_cost = spatial_distance / frame_diagonal

        # 2. Advanced energy similarity cost with zoom scaling validation
        detection_energy = self.calculate_laplacian_energy(frame, detection.bbox, zoom_level)
        energy_cost = 0.0

        if len(track.energy_history) > 0:
            # Expected energy based on zoom scaling: E ∝ zoom²
            zoom_ratio = zoom_level / track.zoom_level if track.zoom_level > 0 else 1.0
            expected_energy = track.laplacian_energy * (zoom_ratio ** 2)

            # Energy consistency validation (from your Chain-of-Zoom work)
            energy_diff = abs(detection_energy - expected_energy)
            energy_variance = np.var(track.energy_history) + 1e-6

            # Normalized energy cost with zoom scaling validation
            energy_cost = energy_diff / (expected_energy + energy_variance)

            # Penalize extreme energy deviations (indicates potential ID switch)
            if energy_diff > 3 * np.sqrt(energy_variance):
                energy_cost *= 2.0  # Strong penalty for inconsistent energy

        # 3. Motion consistency cost
        motion_cost = 0.0
        if hasattr(track, 'velocity') and np.linalg.norm(track.velocity) > 0:
            # Predict next position based on velocity
            predicted_pos = (
                track.center[0] + track.velocity[0],
                track.center[1] + track.velocity[1]
            )

            motion_distance = np.sqrt(
                (predicted_pos[0] - det_center[0]) ** 2 +
                (predicted_pos[1] - det_center[1]) ** 2
            )
            motion_cost = motion_distance / frame_diagonal

        # 4. Face-based association cost (PRIMARY for ID preservation)
        face_cost = 0.0
        if track.has_proxy_feature and detection.has_proxy_feature and track.proxy_bbox and detection.proxy_bbox:
            # Both have faces - use face-specific matching

            # Face spatial distance
            track_face_center = (
                (track.proxy_bbox[0] + track.proxy_bbox[2]) / 2,
                (track.proxy_bbox[1] + track.proxy_bbox[3]) / 2
            )
            det_face_center = (
                (detection.proxy_bbox[0] + detection.proxy_bbox[2]) / 2,
                (detection.proxy_bbox[1] + detection.proxy_bbox[3]) / 2
            )

            face_distance = np.sqrt(
                (track_face_center[0] - det_face_center[0]) ** 2 +
                (track_face_center[1] - det_face_center[1]) ** 2
            )
            face_spatial_cost = face_distance / frame_diagonal

            # Face energy similarity
            detection_face_energy = self.calculate_face_energy(frame, detection.proxy_bbox, zoom_level)
            if hasattr(track, 'face_energy_history') and len(track.face_energy_history) > 0:
                track_face_energy_mean = np.mean(track.face_energy_history)
                face_energy_diff = abs(detection_face_energy - track_face_energy_mean)
                face_energy_cost = face_energy_diff / (track_face_energy_mean + 1e-6)
            else:
                face_energy_cost = 0.0

            # Face size consistency
            track_face_area = (track.proxy_bbox[2] - track.proxy_bbox[0]) * (track.proxy_bbox[3] - track.proxy_bbox[1])
            det_face_area = (detection.proxy_bbox[2] - detection.proxy_bbox[0]) * (detection.proxy_bbox[3] - detection.proxy_bbox[1])

            if track_face_area > 0:
                face_size_ratio = det_face_area / track_face_area
                face_size_cost = abs(1.0 - face_size_ratio)  # Penalty for size changes
            else:
                face_size_cost = 0.0

            # Combined face cost
            face_cost = 0.4 * face_spatial_cost + 0.4 * face_energy_cost + 0.2 * face_size_cost

        elif track.has_proxy_feature != detection.has_proxy_feature:
            # Face presence mismatch - significant penalty
            face_cost = 0.8
        else:
            # Neither has face - use confidence difference
            if track.has_proxy_feature and detection.has_proxy_feature:
                if len(track.proxy_confidence_history) > 0:
                    track_proxy_mean = np.mean(track.proxy_confidence_history)
                    proxy_diff = abs(detection.proxy_confidence - track_proxy_mean)
                    face_cost = proxy_diff
            else:
                face_cost = 0.2  # Small penalty for no faces

        # 5. Combine costs with face-focused weights
        total_cost = (
            self.face_weight * face_cost +        # PRIMARY: Face-based matching
            self.energy_weight * energy_cost +    # SECONDARY: Energy consistency
            self.spatial_weight * spatial_cost +  # SECONDARY: Spatial proximity
            self.motion_weight * motion_cost      # TERTIARY: Motion prediction
        )

        # 6. Zoom change penalty for extreme zoom operations
        zoom_change = abs(zoom_level - self.current_zoom_level)
        if zoom_change >= (self.extreme_zoom_threshold - 1.0):
            # Apply Chain-of-Zoom principles for extreme zoom changes
            total_cost *= 0.8  # Reduce cost to maintain ID consistency
            self.zoom_decompositions += 1

        return total_cost
    
    def associate_detections(self, detections: List[Detection], frame: np.ndarray, zoom_level: float) -> Tuple[List[Tuple[int, int]], List[int], List[int]]:
        """
        🔗 Associate detections with existing tracks using Hungarian algorithm
        
        Args:
            detections: List of new detections
            frame: Current frame
            zoom_level: Current zoom level
            
        Returns:
            (matches, unmatched_detections, unmatched_tracks)
        """
        if len(self.tracks) == 0:
            return [], list(range(len(detections))), []
        
        if len(detections) == 0:
            return [], [], list(self.tracks.keys())
        
        # Build cost matrix
        track_ids = list(self.tracks.keys())
        cost_matrix = np.zeros((len(track_ids), len(detections)))
        
        for i, track_id in enumerate(track_ids):
            track = self.tracks[track_id]
            for j, detection in enumerate(detections):
                cost_matrix[i, j] = self.calculate_association_cost(
                    track, detection, frame, zoom_level
                )
        
        # Solve assignment problem
        row_indices, col_indices = linear_sum_assignment(cost_matrix)
        
        # Filter matches by cost threshold
        matches = []
        matched_detection_indices = set()
        matched_track_indices = set()
        
        for row, col in zip(row_indices, col_indices):
            if cost_matrix[row, col] < 0.7:  # Association threshold
                track_id = track_ids[row]
                matches.append((track_id, col))
                matched_detection_indices.add(col)
                matched_track_indices.add(row)
        
        # Find unmatched detections and tracks
        unmatched_detections = [
            i for i in range(len(detections)) 
            if i not in matched_detection_indices
        ]
        
        unmatched_tracks = [
            track_ids[i] for i in range(len(track_ids))
            if i not in matched_track_indices
        ]
        
        return matches, unmatched_detections, unmatched_tracks

    def update_track(self, track_id: int, detection: Detection, frame: np.ndarray, zoom_level: float):
        """
        🔄 Update existing track with new detection

        Args:
            track_id: Track ID to update
            detection: New detection
            frame: Current frame
            zoom_level: Current zoom level
        """
        track = self.tracks[track_id]

        # Update spatial information
        track.bbox = detection.bbox
        new_center = (
            (detection.bbox[0] + detection.bbox[2]) / 2,
            (detection.bbox[1] + detection.bbox[3]) / 2
        )

        # Update velocity (simple difference)
        if track.center != (0.0, 0.0):
            track.velocity = (
                new_center[0] - track.center[0],
                new_center[1] - track.center[1]
            )

        track.center = new_center

        # Update energy features
        new_energy = self.calculate_laplacian_energy(frame, detection.bbox, zoom_level)
        track.laplacian_energy = new_energy
        track.energy_history.append(new_energy)
        track.zoom_level = zoom_level
        track.zoom_adjusted_energy = new_energy

        # Update face energy if face is present
        if detection.has_proxy_feature and detection.proxy_bbox:
            face_energy = self.calculate_face_energy(frame, detection.proxy_bbox, zoom_level)
            track.face_energy = face_energy
            track.face_energy_history.append(face_energy)

        # Update proxy feature information
        track.has_proxy_feature = detection.has_proxy_feature
        track.proxy_bbox = detection.proxy_bbox
        track.proxy_confidence = detection.proxy_confidence
        track.proxy_confidence_history.append(detection.proxy_confidence)

        # Update tracking state
        track.hits += 1
        track.time_since_update = 0
        track.last_seen = time.time()

        # Confirm track if enough hits
        if track.hits >= config.min_track_hits:
            track.confirmed = True

    def create_track(self, detection: Detection, frame: np.ndarray, zoom_level: float) -> int:
        """
        🆕 Create new track from detection

        Args:
            detection: Detection to create track from
            frame: Current frame
            zoom_level: Current zoom level

        Returns:
            New track ID
        """
        track_id = self.next_track_id
        self.next_track_id += 1

        # Calculate initial energy
        initial_energy = self.calculate_laplacian_energy(frame, detection.bbox, zoom_level)

        # Create track
        track = Track(
            track_id=track_id,
            bbox=detection.bbox,
            center=(
                (detection.bbox[0] + detection.bbox[2]) / 2,
                (detection.bbox[1] + detection.bbox[3]) / 2
            ),
            laplacian_energy=initial_energy,
            has_proxy_feature=detection.has_proxy_feature,
            proxy_bbox=detection.proxy_bbox,
            proxy_confidence=detection.proxy_confidence,
            zoom_level=zoom_level,
            zoom_adjusted_energy=initial_energy
        )

        # Initialize history
        track.energy_history.append(initial_energy)
        track.proxy_confidence_history.append(detection.proxy_confidence)

        self.tracks[track_id] = track

        return track_id

    def cleanup_tracks(self):
        """🧹 Remove old/lost tracks"""
        tracks_to_remove = []

        for track_id, track in self.tracks.items():
            if track.time_since_update > config.max_track_age:
                tracks_to_remove.append(track_id)

        for track_id in tracks_to_remove:
            del self.tracks[track_id]

    def update_zoom_state_history(self, frame: np.ndarray, tracks: List[Track], zoom_level: float):
        """
        📊 Update zoom state history for AR-2 modeling (from your Chain-of-Zoom work)

        Args:
            frame: Current frame
            tracks: Current tracks
            zoom_level: Current zoom level
        """
        # Create zoom state snapshot
        zoom_state = {
            'frame_id': self.frame_count,
            'zoom_level': zoom_level,
            'track_energies': {track.track_id: track.laplacian_energy for track in tracks},
            'track_positions': {track.track_id: track.center for track in tracks},
            'timestamp': time.time()
        }

        self.zoom_state_history.append(zoom_state)
        self.current_zoom_level = zoom_level

    def update(self, detections: List[Detection], frame: np.ndarray, zoom_level: float = 1.0) -> List[Track]:
        """
        🚀 Enhanced tracking update with Chain-of-Zoom principles

        Args:
            detections: List of new detections
            frame: Current frame
            zoom_level: Current zoom level

        Returns:
            List of confirmed tracks with preserved IDs
        """
        start_time = time.time()
        self.frame_count += 1

        # Check for extreme zoom changes (Chain-of-Zoom activation)
        zoom_change = abs(zoom_level - self.current_zoom_level)
        is_extreme_zoom = zoom_change >= (self.extreme_zoom_threshold - 1.0)

        if is_extreme_zoom:
            # Apply Chain-of-Zoom principles for better ID preservation
            # Reduce association threshold for extreme zoom changes
            original_threshold = 0.7
            association_threshold = 0.9  # More lenient for extreme zoom
        else:
            association_threshold = 0.7  # Normal threshold

        # Predict existing tracks
        self.predict_tracks()

        # Associate detections with tracks using enhanced cost function
        matches, unmatched_detections, unmatched_tracks = self.associate_detections(
            detections, frame, zoom_level
        )

        # Filter matches by enhanced threshold
        filtered_matches = []
        for track_id, detection_idx in matches:
            track = self.tracks[track_id]
            detection = detections[detection_idx]
            cost = self.calculate_association_cost(track, detection, frame, zoom_level)

            if cost < association_threshold:
                filtered_matches.append((track_id, detection_idx))
            else:
                unmatched_detections.append(detection_idx)
                unmatched_tracks.append(track_id)

        # Update matched tracks
        for track_id, detection_idx in filtered_matches:
            self.update_track(track_id, detections[detection_idx], frame, zoom_level)

        # Create new tracks for unmatched detections (with caution during zoom)
        if not is_extreme_zoom or len(unmatched_detections) <= 2:  # Limit new tracks during zoom
            for detection_idx in unmatched_detections:
                self.create_track(detections[detection_idx], frame, zoom_level)

        # Cleanup old tracks (more conservative during zoom)
        if not is_extreme_zoom:
            self.cleanup_tracks()

        # Update zoom state history for AR-2 modeling
        confirmed_tracks = [track for track in self.tracks.values() if track.confirmed]
        self.update_zoom_state_history(frame, confirmed_tracks, zoom_level)

        # Track performance
        tracking_time = time.time() - start_time
        self.tracking_times.append(tracking_time)

        if len(self.tracking_times) > 100:
            self.tracking_times = self.tracking_times[-100:]

        return confirmed_tracks

    def get_track_by_id(self, track_id: int) -> Optional[Track]:
        """Get track by ID"""
        return self.tracks.get(track_id)

    def get_all_tracks(self) -> List[Track]:
        """Get all tracks (confirmed and unconfirmed)"""
        return list(self.tracks.values())

    def get_confirmed_tracks(self) -> List[Track]:
        """Get only confirmed tracks"""
        return [track for track in self.tracks.values() if track.confirmed]

    def get_performance_stats(self) -> Dict[str, Any]:
        """Get enhanced tracker performance statistics"""
        if not self.tracking_times:
            return {
                "avg_time": 0.0,
                "fps": 0.0,
                "total_tracks": 0,
                "confirmed_tracks": 0,
                "id_switches": 0,
                "zoom_decompositions": 0
            }

        avg_time = np.mean(self.tracking_times)
        fps = 1.0 / avg_time if avg_time > 0 else 0.0

        return {
            "avg_time": avg_time,
            "fps": fps,
            "total_tracks": len(self.tracks),
            "confirmed_tracks": len(self.get_confirmed_tracks()),
            "frame_count": self.frame_count,
            "id_switches": self.id_switches,
            "zoom_decompositions": self.zoom_decompositions,
            "current_zoom_level": self.current_zoom_level,
            "zoom_state_history_length": len(self.zoom_state_history)
        }
